package com.leiden.utils;

import java.io.IOException;
import java.net.InetAddress;

public class Util {

    public static void threadSleep(long time){
        try{
            Thread.sleep(time);
        }catch (Exception e){

        }
    }

    public static boolean startPing(String ip){
        boolean success=false;
        Process p =null;
        try {
            success = InetAddress.getByName(ip).isReachable(3000);
        } catch (IOException e) {
            e.printStackTrace();
        } finally{
            if(p != null) {
                p.destroy();
            }
        }
        return success;
    }


}
