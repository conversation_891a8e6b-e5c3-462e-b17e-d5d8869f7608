package com.leiden.utils;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;

public class SharedPreferencesUtil {

    private static String getCachePath() {
        return ParameterUtil.getSDKRoot() + File.separator + "cache" + File.separator;
    }

    public static String getContent(String key){

        if (key == null || key.trim().equals("")) {
            return null;
        }

        File keyFile = new File(getCachePath() + key);
        if (!keyFile.exists()) {
            return null;
        }
        if (keyFile.length() == 0) {
            return "";
        }

        try {
            return FileUtils.readFileToString(keyFile, "utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean setContent(String key, String content){

        if (key == null || key.trim().equals("") || content == null) {
            return false;
        }

        File keyFile = new File(getCachePath() + key);
        if (keyFile.exists()) {
            keyFile.delete();
        }

        keyFile.getParentFile().mkdirs();

        boolean createNewFile;
        try {
            createNewFile = keyFile.createNewFile();
            if (!createNewFile) {
                return false;
            }
            FileUtils.write(keyFile, content, "utf-8");
            // 不要判断文件是否有内容  文件里面可能没有内容
            return keyFile.exists();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }


}
