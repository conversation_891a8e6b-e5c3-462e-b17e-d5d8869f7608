package com.leiden.utils;

public class BinaryConversionUtil {

    private static final char[] HEX_CHAR = {'0', '1', '2', '3', '4', '5',
            '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    //字节转化成十六进制
    public static String bytesToHexFun(byte[] bytes) {
        char[] buf = new char[bytes.length * 2];
        int index = 0;
        for (byte b : bytes) { // 利用位运算进行转换，可以看作方法一的变种
            buf[index++] = HEX_CHAR[b >>> 4 & 0xf];
            buf[index++] = HEX_CHAR[b & 0xf];
        }
        return new String(buf);
    }

    public static String byteToHexFun(byte tempByte){
        char[] buf = new char[2];
        buf[0] = HEX_CHAR[tempByte >>> 4 & 0xf];
        buf[1] = HEX_CHAR[tempByte & 0xf];
        return new String(buf);
    }



}
