package com.leiden.utils;


import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

public class ImageUtil {

    /**
     * 保存图片的方法
     */
    public static String saveBitmap(BufferedImage iamge, String saveName, String saveRootPath) {
        File file = new File(saveRootPath);
        if(!file.exists()){
            file.mkdirs();
        }

        File f = new File(saveRootPath, saveName);
        if (f.exists()) {
            f.delete();
        }
        try {
            FileOutputStream out = new FileOutputStream(f);
            boolean result = ImageIO.write(iamge, "png", out);
            out.flush();
            out.close();
            LogUtils.log("已经保存");
            if (result) {
                return f.getAbsolutePath();
            }
        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 处理图片的大小
     *
     * @param bitmap
     * @param newBitmapW
     * @param newBitmapH
     * @return
     */
    public static BufferedImage handleBitmap(BufferedImage bitmap, int newBitmapW, int newBitmapH) {
        final BufferedImage outputImage = new BufferedImage(newBitmapW, newBitmapH, bitmap.getType());
        Graphics2D createGraphics = outputImage.createGraphics();

        // 设置高质量绘制参数
        createGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        createGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        createGraphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

        createGraphics.drawImage(bitmap, 0, 0, newBitmapW, newBitmapH, null);
        createGraphics.dispose();

        return outputImage;
    }
}
