package com.leiden.manager;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ThreadExecutorManager {


    //维护一个线程池
    private ExecutorService cachedThreadPool = Executors.newCachedThreadPool();

    private static class ThreadExecutorManagerHolder {
        private static ThreadExecutorManager instance = new ThreadExecutorManager();
    }

    public static ThreadExecutorManager getInstance() {
        return ThreadExecutorManagerHolder.instance;
    }

    public void execute(Runnable runnable){
        cachedThreadPool.execute(runnable);
    }

}
