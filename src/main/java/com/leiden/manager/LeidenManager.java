package com.leiden.manager;


import com.leiden.interfaces.LeidenPrintfResultCallBack;
import com.leiden.interfaces.LeidenSendReadCallBack;
import com.leiden.interfaces.LiedenPrinterStatusCallBack;
import com.leiden.manager.connect.LeidenDeviceManager;
import com.leiden.model.LeidenEnvPrinterModel;
import com.leiden.model.LeidenPrinterModel;
import com.leiden.model.LeidenRfidModel;
import com.leiden.model.LeidenSmallBitmapModel;
import com.leiden.utils.*;
import org.apache.commons.io.FileUtils;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class LeidenManager {

    private LeidenDeviceManager deviceManager;

    private LeidenEnvPrinterModel leidenEnvPrinterModel = new LeidenEnvPrinterModel();

    public LeidenEnvPrinterModel getLeidenEnvPrinterModel() {
        return leidenEnvPrinterModel;
    }

    static class PrintfAPLManagerHolder {
        private static LeidenManager instance = new LeidenManager();
    }

    public static LeidenManager getInstance() {
        if (PrintfAPLManagerHolder.instance.deviceManager == null) {
            PrintfAPLManagerHolder.instance.deviceManager = LeidenDeviceManager.getInstance();
        }
        return PrintfAPLManagerHolder.instance;
    }


    /**
     * 设置当前的环境变量
     */
    public int setCurrentEnviron(LeidenEnvPrinterModel leidenEnvPrinterModel) {
        // if (!deviceManager.isConnect()) {
        //     return 1;
        // }
        if (leidenEnvPrinterModel != null) {
            if (leidenEnvPrinterModel.equals(this.leidenEnvPrinterModel)) {
                return 2;
            }
        } else {
            leidenEnvPrinterModel = this.leidenEnvPrinterModel;
        }
        final int printerAccuracy = leidenEnvPrinterModel.getPrinterAccuracy();

        String appCMD = leidenEnvPrinterModel.getAppCMD();
        SharedPreferencesUtil.setContent("currentEnviron", appCMD);
        getCurrentEnviron();

        if (printerAccuracy != LeidenManager.this.leidenEnvPrinterModel.getPrinterAccuracy()) {
            LeidenManager.this.leidenEnvPrinterModel.setPrinterAccuracy(printerAccuracy);
        }

        return 0;
    }

    public void getCurrentEnviron() {
        getCurrentEnviron(null);
    }

    /**
     * 取得当前环境
     */
    public void getCurrentEnviron(final GetCurrentEnvironCallBack getCurrentEnvironCallBack) {
        String currentEnviron = SharedPreferencesUtil.getContent("currentEnviron");
        if (currentEnviron == null) {
            currentEnviron = leidenEnvPrinterModel.getAppCMD();
            SharedPreferencesUtil.setContent("currentEnviron", currentEnviron);
        }
        leidenEnvPrinterModel.analysis(currentEnviron);
        if (getCurrentEnvironCallBack != null) {
            getCurrentEnvironCallBack.callBack(currentEnviron);
        }
    }


    public interface GetCurrentEnvironCallBack {
        void callBack(String data);

        void error(String error);
    }


    /**
     * 得到打印机的环境设置
     * DEF DK=8,MD=1,PW=384,PH=344
     */
    public String getPrinterEnvironmentSetCMD(LeidenPrinterModel leidenPrinterModel) {

        int labelH = leidenPrinterModel.getLabelH();
        int labelW = leidenPrinterModel.getLabelW();

        StringBuilder sb = new StringBuilder();
        sb.append("DEF ")
                .append("PW=")
                .append(labelW)
                .append(",PH=").append(labelH);
        String appCMD = this.leidenEnvPrinterModel.getAppCMD();
        sb.append(",").append(appCMD).append("\n");

        return sb.toString();
    }


    public void printfAPLLabels(final List<LeidenPrinterModel> leidenPrinterModels, final LeidenPrintfResultCallBack leidenPrintfResultCallBack) {
        final ThreadExecutorManager threadExecutorManager = ThreadExecutorManager.getInstance();

        boolean connect = deviceManager.isConnect();
        if (!connect) {
            if (leidenPrintfResultCallBack != null) {
                leidenPrintfResultCallBack.callBack(LeidenPrintfResultCallBack.LEIDEN_PRINTF_RESULT_BLUETOOTH);
            }
            return;
        }
        threadExecutorManager.execute(new Runnable() {
            @Override
            public void run() {

                final int result = printfLabel(leidenPrinterModels, leidenPrintfResultCallBack);
                if (result != 1) {
                    if (leidenPrintfResultCallBack != null) {
                        leidenPrintfResultCallBack.callBack(LeidenPrintfResultCallBack.LEIDEN_PRINTF_RESULT_CMD_ERROR);
                    }
                }

                leidenPrintfResultCallBack.callBack(LeidenPrintfResultCallBack.LEIDEN_PRINTF_RESULT_SUCCESS);

            }
        });
    }

    public void getPrinterStatus(LiedenPrinterStatusCallBack callBack) {

        if (!deviceManager.isConnect()) {
            return;
        }

        String sesc = "SESC TP=1\r\n";

        deviceManager.write(sesc.getBytes());

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        // byte[] temp = ("S").getBytes();
        byte[] temp1 = new byte[2];
        temp1[0] = 27;
        temp1[1] = 83;

        deviceManager.sendReadAsync(temp1, 500, 20, new LeidenSendReadCallBack() {

            @Override
            public void callBytes(byte[] bytes) {
                LogUtils.log("收到数据：" + new String(bytes));
                if (callBack != null) {
                    String resultStr = new String(bytes);
                    int result = Integer.parseInt(resultStr.replace("\r\n", ""));
                    callBack.callResult(result);
                }
            }

            @Override
            public void callError(int error) {
                LogUtils.log("收error：" + error);
                if (callBack != null) {
                    callBack.callResult(-1);
                }
            }

        });

    }


    public void printfAPLLabel(final LeidenPrinterModel leidenPrinterModel, final LeidenPrintfResultCallBack leidenPrintfResultCallBack) {

        final ThreadExecutorManager threadExecutorManager = ThreadExecutorManager.getInstance();
        threadExecutorManager.execute(new Runnable() {
            @Override
            public void run() {
                boolean connect = deviceManager.isConnect();
                if (!connect) {
                    if (leidenPrintfResultCallBack != null) {
                        leidenPrintfResultCallBack.callBack(LeidenPrintfResultCallBack.LEIDEN_PRINTF_RESULT_BLUETOOTH);
                    }
                    return;
                }

                // 判断打印机状态


                List<LeidenPrinterModel> leidenPrinterModels = new ArrayList<>();
                leidenPrinterModels.add(leidenPrinterModel);
                int result = realPrintfLabel(leidenPrinterModels, leidenPrintfResultCallBack);

                /**
                 * -1:数据发送失败 蓝牙未连接
                 * 1:数据发送成功
                 * -2:数据发送失败 抛出异常 失败
                 */
                if (result == 1) {
                    if (leidenPrintfResultCallBack != null) {
                        leidenPrintfResultCallBack.callBack(LeidenPrintfResultCallBack.LEIDEN_PRINTF_RESULT_SUCCESS);
                    }
                } else {
                    if (leidenPrintfResultCallBack != null) {
                        leidenPrintfResultCallBack.callBack(LeidenPrintfResultCallBack.LEIDEN_PRINTF_RESULT_CMD_ERROR);
                    }
                }
            }
        });
    }

    /**
     * 在一次任务中打印多张 每个model的环境必须是一致的 宽高与打印方向
     *
     * @param leidenPrinterModels
     * @return
     */
    private int printfLabel(List<LeidenPrinterModel> leidenPrinterModels, LeidenPrintfResultCallBack leidenPrintfResultCallBack) {
        return realPrintfLabel(leidenPrinterModels, leidenPrintfResultCallBack);
    }

    private int realPrintfLabel(List<LeidenPrinterModel> leidenPrinterModels, LeidenPrintfResultCallBack leidenPrintfResultCallBack) {
        LeidenPrinterModel tempLeidenPrinterModel = leidenPrinterModels.get(0);

        StringBuilder textSb = new StringBuilder();
        textSb.append("JOB\n");
        textSb.append(getPrinterEnvironmentSetCMD(tempLeidenPrinterModel));

        for (int j = 0; j < leidenPrinterModels.size(); j++) {
            textSb.append("START\n");

            LeidenRfidModel leidenRfidModel = leidenPrinterModels.get(j).getLeidenRfidModel();

            if (leidenRfidModel != null && leidenRfidModel.isAvailable()) {
                textSb.append(leidenRfidModel.getCmd());
            }

            List<LeidenSmallBitmapModel> leidenSmallBitmapModels
                    = leidenPrinterModels.get(j).getLeidenSmallBitmapModels();

            for (int i = 0; i < leidenSmallBitmapModels.size(); i++) {
                LeidenSmallBitmapModel leidenSmallBitmapModel = leidenSmallBitmapModels.get(i);

                // 处理一下图片尺寸 应该是 8 的倍数
                handleBitmapModel(leidenSmallBitmapModel);

                String bitmapCMD = getBitmapCMD(leidenSmallBitmapModel);
                textSb.append(bitmapCMD);
            }

            textSb.append("QTY P=").append(tempLeidenPrinterModel.getNumber()).append("\n");
            textSb.append("END\n");
        }
        textSb.append("JOBE\n");

        try {
            FileUtils.write(new File(ParameterUtil.getSDKRoot(), "sendData.txt"), textSb, "UTF-8");
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        byte[] data = textSb.toString().getBytes();

        if (!deviceManager.isConnect()) {
            return -1;
        }

        int length = 1024 * 8;
        byte[] sendData = new byte[length];
        int index = 0;
        while (index < data.length) {
            if (!deviceManager.isConnect()) {
                return -1;
            }
            if (data.length - index >= length) {
                System.arraycopy(data, index, sendData, 0, length);
            } else {
                sendData = new byte[data.length - index];
                System.arraycopy(data, index, sendData, 0, data.length - index);
            }
            index += length;
            if (deviceManager.write(sendData) == 1) {
                if (leidenPrintfResultCallBack != null) {
                    leidenPrintfResultCallBack.progress(data.length, index);
                }
                //0 或者正数表示成功
            } else {
                int i = 0;
                while (i < 20) {
                    if (deviceManager.write(sendData) == 1) {
                        if (leidenPrintfResultCallBack != null) {
                            leidenPrintfResultCallBack.progress(data.length, index);
                        }
                        break;
                    } else {
                        i++;
                        Util.threadSleep(100);
                    }
                }
                if (i >= 20) {
                    return -1;
                }
            }
        }
        return 1;
    }

    private void handleBitmapModel(LeidenSmallBitmapModel leidenSmallBitmapModel) {

        int bitmapW = leidenSmallBitmapModel.getWD();
        int remainder = bitmapW % 8;
        if (remainder != 0) {
            bitmapW += 8 - remainder;
        }
        leidenSmallBitmapModel.setWD(bitmapW);
    }

    public String getBitmapCMD(LeidenSmallBitmapModel leidenSmallBitmapModel) {
        StringBuilder sb = new StringBuilder();
        //处理图片的尺寸

        BufferedImage bitmap = leidenSmallBitmapModel.getBitmap();

//        bitmap = ImageUtil.handleBitmap(bitmap, leidenSmallBitmapModel.getWD(), leidenSmallBitmapModel.getHT());
        if (ParameterUtil.isDebug) {
            String path = ParameterUtil.getSDKRoot();
            String savePath = ImageUtil.saveBitmap(bitmap, "leidenBitmap.png", path);
            LogUtils.log("leiden debug 保存路径：" + savePath);
        }
        sb.append("GRAPH X=").append(leidenSmallBitmapModel.getX())
                .append(",Y=").append(leidenSmallBitmapModel.getY())
                .append(",WD=").append(bitmap.getWidth())
                .append(",HT=").append(bitmap.getHeight())
                .append(",MD=1\n");
        sb.append(convertToBMW(bitmap, 128));
        sb.append("\n");
        return sb.toString();
    }

    /**
     * 图片二值化
     *
     * @param bmp
     * @return
     */
    public String convertToBMW(BufferedImage bmp, int concentration) {

        //求出当前图片的半色调阈值
//        bmp = ImageUtil.convertGreyImgByFloyd(bmp, context);

        StringBuilder sb = new StringBuilder();

        if (concentration <= 0 || concentration >= 255) {
            concentration = 128;
        }
        int width = bmp.getWidth(); // 获取位图的宽
        int height = bmp.getHeight(); // 获取位图的高
        int[] p = new int[8];
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width / 8; j++) {
                for (int z = 0; z < 8; z++) {
                    int grey = bmp.getRGB(j * 8 + z, i);
                    // LogUtils.log("grey:" + grey);
                    int red = ((grey & 0x00FF0000) >> 16);
                    int green = ((grey & 0x0000FF00) >> 8);
                    int blue = (grey & 0x000000FF);
                    int gray = (int) (0.29900 * red + 0.58700 * green + 0.11400 * blue); // 灰度转化公式
//                    int gray = (int) ((float) red * 0.3 + (float) green * 0.59 + (float) blue * 0.11);
                    if (gray <= concentration) {
                        gray = 1;
                    } else {
                        gray = 0;

                    }
                    p[z] = gray;
                }
                byte value = (byte) (p[0] * 128 + p[1] * 64 + p[2] * 32 + p[3] * 16 + p[4] * 8 + p[5] * 4 + p[6] * 2 + p[7]);
                sb.append(BinaryConversionUtil.byteToHexFun(value));
            }
        }
        return sb.toString();
    }

}
