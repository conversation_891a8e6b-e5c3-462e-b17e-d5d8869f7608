package com.leiden.manager.connect;

import com.leiden.interfaces.LeidenConnectResultCallBack;
import com.leiden.interfaces.LeidenIConnectInterface;
import com.leiden.interfaces.LeidenScanDeviceCallBack;
import com.leiden.interfaces.LeidenSendReadCallBack;
import com.leiden.model.LeidenDevice;
import com.leiden.model.WifiDevice;
import com.leiden.utils.LogUtils;
import com.leiden.utils.ParameterUtil;
import com.leiden.utils.Util;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

public class LeidenDeviceManager implements LeidenIConnectInterface {

    private volatile static LeidenDeviceManager instance;

    private LeidenDeviceManager() {
    }

    public static LeidenDeviceManager getInstance(){
        if (instance == null) {
            synchronized (LeidenDeviceManager.class) {
                if (instance == null) {
                    instance = new LeidenDeviceManager();
                }
            }
        }
        return instance;
    }

    private Socket socket = null;
    private InputStream reader=null;
    private OutputStream writer=null;

    private LeidenDevice currentDevice = null;

    /**
     * 连接的结果回调
     */
    private LeidenConnectResultCallBack connectResultCallBack = null;

    @Override
    public boolean isConnect() {
        return socket != null && socket.isConnected();
    }

    @Override
    public boolean connect(final LeidenDevice device) {

        threadExecutorManager.execute(new Runnable() {
            @Override
            public void run() {
                WifiDevice wifiDevice = device.getWifiDevice();
                if(wifiDevice == null){
                    return;
                }

                String ip = wifiDevice.getIp();
                int port = wifiDevice.getPort();

                if(isConnect()){
                    close();
                }

                try {
                    socket = new Socket(ip, port);
                    reader = socket.getInputStream();
                    writer = socket.getOutputStream();
                    currentDevice = device;
                    noticeConnectResult(true);
                    beginCheckWifiState();
                } catch (IOException e) {
                    e.printStackTrace();
                    noticeConnectResult(false);
                    return;
                }
            }
        });

        return true;
    }

    @Override
    public boolean close() {
        try {
            if(reader != null){
                reader.close();
                reader = null;
            }

            if(writer != null){
                writer.close();
                writer = null;
            }

            if(socket != null) {
                socket.close();
                socket = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            reader = null;
            writer = null;
            socket = null;
        }
        if(connectResultCallBack != null){
            connectResultCallBack.close(currentDevice);
        }
        isBeginCheckWifiState = false;
        return true;
    }

    @Override
    public byte[] read() {

        if(isConnect()){

            byte[] readBuff = null;
            int readLen = 0;
            try {
                readLen = this.reader.available();
                LogUtils.log("readLen:" + readLen);
                if (this.reader != null && (readLen) > 0) {
                    readBuff = new byte[readLen];

                    if (this.reader.read(readBuff) == -1) {
                        return null;
                    }
                    LogUtils.log("readBuff:" + readBuff);
                }
            } catch (IOException var3) {
                var3.printStackTrace();
            }
            return readBuff;

        }

        return new byte[0];
    }

    @Override
    public int write(byte[] data) {
        try {
            if (!isConnect()) {
                return -1;
            }
            if (writer != null) {
                writer.write(data);
                writer.flush();
            } else {
                return -2;
            }
            return 1;
        } catch (IOException e) {
            e.printStackTrace();
            return -2;
        }
    }

    @Override
    public void sendReadAsync(final byte[] bytes, final int time, final int number, final LeidenSendReadCallBack sendBytesToReadCallBack) {
        if (!isConnect()) {
            if (sendBytesToReadCallBack != null) {
                sendBytesToReadCallBack.callError(3);
            }
            return;
        }

        threadExecutorManager.execute(new Runnable() {
            @Override
            public void run() {
                sendRead(bytes, time, number, sendBytesToReadCallBack);
            }
        });
    }

    @Override
    public void sendRead(byte[] bytes, int time, int number, final LeidenSendReadCallBack sendBytesToReadCallBack) {
        try {
            //总的等待时间，如果超过了20秒，则归结为20秒
            if (time * number > 10 * 2000) {
                time = 500;
                number = 40;
            }
            read();
            if (ParameterUtil.isDebug) {
                StringBuilder sb = new StringBuilder();
                for (byte aByte : bytes) {
                    sb.append(aByte).append(",");
                }
                LogUtils.log("我要发送数据了" + sb.toString());
            }

            int write = write(bytes);
            LogUtils.log("write: " + write);
            if (write == 1) {
                int i = 0;
                while (i < number) {
                    Util.threadSleep(time);
                    if (isConnect()) {
                        final byte[] read = read();
                        if (read != null) {
                            if (ParameterUtil.isDebug) {
                                StringBuilder sb1 = new StringBuilder();
                                for (byte b : read) {
                                    sb1.append(b).append(",");
                                }
                                LogUtils.log("我得到数据了" + sb1.toString());
                            }

                            if (sendBytesToReadCallBack != null) {
                                sendBytesToReadCallBack.callBytes(read);
                            }

                            return;
                        }
                        i++;
                    } else {
                        if (sendBytesToReadCallBack != null) {
                            sendBytesToReadCallBack.callError(3);
                        }
                        return;
                    }

                }
            }

            if (sendBytesToReadCallBack != null) {
                sendBytesToReadCallBack.callError(1);
            }

        } catch (Exception e) {

            if (sendBytesToReadCallBack != null) {
                sendBytesToReadCallBack.callError(2);
            }

        }
    }


    @Override
    public boolean autoConnect() {
        return false;
    }

    @Override
    public int beginSearch() {
        return 0;
    }

    @Override
    public void stopSearch() {

    }

    @Override
    public LeidenDevice getCurrentDevice() {
        return currentDevice;
    }

    @Override
    public void setLeidenScanDeviceCallBack(LeidenScanDeviceCallBack leidenScanDeviceCallBack) {

    }

    @Override
    public void setLeidenConnectResultCallBack(LeidenConnectResultCallBack leidenConnectResultCallBack) {
        this.connectResultCallBack = leidenConnectResultCallBack;
    }

    /**
     * 通知连接结果
     */
    private void noticeConnectResult(final boolean isSuccess) {
        if (connectResultCallBack != null) {
            if (!isSuccess) {
                connectResultCallBack.fail(currentDevice);
            } else {
                LogUtils.log("连接成功");
                connectResultCallBack.success(currentDevice);
            }
        }
    }

    private boolean isBeginCheckWifiState = false;

    private void beginCheckWifiState(){
        if(isBeginCheckWifiState){
            return;
        }
        isBeginCheckWifiState = true;
        threadExecutorManager.execute(new Runnable() {
            @Override
            public void run() {
                while(true) {

                    if(isConnect() && currentDevice != null && currentDevice.getWifiDevice() != null) {
                        WifiDevice wifiDevice = currentDevice.getWifiDevice();
                        if(!Util.startPing(wifiDevice.getIp())){
                            close();
                            return;
                        }
                    }

                    if (!isBeginCheckWifiState) {
                        return;
                    }

                    Util.threadSleep(500);
                }

            }
        });
    }

}
