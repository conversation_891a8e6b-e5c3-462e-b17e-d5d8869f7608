package com.leiden.interfaces;


public interface LiedenPrinterStatusCallBack {

    /**
    result的定义：
    10 在线状态 on-line
    11 打印中 printing
    20 暂停(继续打印)pause(continue printing)
    21 等待剥离 Waiting for stripping 通过剥离传感器实现 By spinning off the sensor
    22 等待剥离 Waiting for stripping 通过按键实现 By key
    24 暂 停 ( 连 续 打 印 时)pause(continual printing)
    25 暂 停 ( 打 印 头 过热)pause(The print head excessive heat
    27 暂 停 ( 用 户 操 作 )pause(user operation) 打 印 中 止 / 打 印 继 续 suspend printing /continue printing
    30 黑标错误 black mark error 检测黑标标签失败 Detection of black mark label is failure
    31 无标签错误 no label error检测到标签已经打印完毕 Detect label has finished printing
    32 切刀错误 cutter error 切纸打印切刀检测到错误 Paper cutting print, detected cutter error
    33 碳带错误 ribbon error 碳带打印时没有检测到碳带 Not detected when printed with ribbon
    34 打印头开盖 open the print head 检测到打印头抬起（需要硬件支持）detect the print head lift (need hardware support)
    40 电池异常 Abnormal battery 电压偏低 The low voltage
    41 EEPROM 异常 Abnormal EEPROM 内部 EEPROM 检测失败 Block internal EEPROM test failure
    42 打印头未连接 The print head not connected 检测到打印线连接异常 The print line connection exception
    43 硬件异常 Hardware Exception
    44 打印头温度过低 The print head temperature is too low环境温度太低或者打印头连线异常 The environment temperature is too low or the print line connection exception
    99 其他状态 other status
    -1 打印机异常 无法正常获取状态
     */
    public void callResult(int result);

}
