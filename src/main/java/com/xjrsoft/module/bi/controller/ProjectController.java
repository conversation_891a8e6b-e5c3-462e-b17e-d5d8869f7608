package com.xjrsoft.module.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.bi.dto.*;
import com.xjrsoft.module.bi.entity.Project;
import com.xjrsoft.module.bi.service.IProjectService;
import com.xjrsoft.module.bi.vo.ProjectInfoVo;
import com.xjrsoft.module.system.entity.Menu;
import com.xjrsoft.module.system.service.IMenuService;
import com.xjrsoft.module.system.vo.MenuVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 项目表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-06
 */
@RestController
@RequestMapping(GlobalConstant.BI_MODULE_PREFIX + "/project")
@Api(value = GlobalConstant.BI_MODULE_PREFIX + "/project", tags = "BI大屏设计模块")
@AllArgsConstructor
public class ProjectController {

    private final IProjectService projectService;

    private final IMenuService menuService;

    @GetMapping(value = "/page")
    @ApiOperation(value = "查询bi大屏列表(分页)")
    public R page(@Valid ProjectPageDto dto) {
        return R.ok(projectService.getPage(dto));
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "查看详情")
    public R info(@RequestParam Long id) {
        Project project = projectService.getById(id);
        return R.ok(BeanUtil.toBean(project, ProjectInfoVo.class));
    }

    @GetMapping(value = "/old")
    @ApiOperation(value = "获取以前发布菜单的数据")
    public R getOldValue(@RequestParam String menuId) {
        Menu menu = menuService.selectJoinOne(Menu.class, MPJWrappers.<Menu>lambdaJoin()
                .disableLogicDel()
                .eq(Menu::getId, menuId)
        );

        return R.ok(BeanUtil.toBean(menu, MenuVo.class));
    }


    @PutMapping("/index-image")
    @ApiOperation(value = "修改项目首页图片")
    public R updateIndexImage(@Valid @RequestBody UpdateProjectIndexImageDto dto) {
        Project project = BeanUtil.toBean(dto, Project.class);
        return R.ok(projectService.updateById(project));
    }

    @PutMapping("/content")
    @ApiOperation(value = "修改项目json")
    public R updateContent(@Valid @RequestBody UpdateProjectContentDto dto) {
        Project project = BeanUtil.toBean(dto, Project.class);
        return R.ok(projectService.updateById(project));
    }

    @PostMapping
    @ApiOperation(value = "创建项目")
    public R add(@Valid @RequestBody AddProjectDto dto) {
        Project project = BeanUtil.toBean(dto, Project.class);
//        project.setContent("{}");
        project.setState(YesOrNoEnum.YES.getCode());
        projectService.save(project);
        return R.ok(project.getId());
    }

    @PutMapping
    @ApiOperation(value = "保存项目")
    public R update(@Valid @RequestBody UpdateProjectDto dto) {
        Project project = BeanUtil.toBean(dto, Project.class);
        projectService.updateById(project);
        return R.ok(project.getId());
    }

    @PutMapping("/publish")
    @ApiOperation(value = "发布/不发布")
    public R publish(@Valid @RequestBody PublishProjectDto dto) {
        Project project = BeanUtil.toBean(dto, Project.class);
        return R.ok(projectService.updateById(project));
    }

    @PostMapping("/publish-menu")
    @ApiOperation(value = "发布菜单")
    public R publishMenu(@Valid @RequestBody PublishMenuDto dto) {
        return R.ok(projectService.publishMenu(dto));
    }

    @PostMapping("/cancel-menu")
    @ApiOperation(value = "取消发布菜单")
    public R cancelMenu(@Valid @RequestBody CancelMenuDto dto) {
        return R.ok(projectService.cancelMenu(dto));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@RequestBody List<Long> ids) {
        return R.ok(projectService.deleteProject(ids));
    }



}
