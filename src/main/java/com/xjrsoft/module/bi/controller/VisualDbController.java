package com.xjrsoft.module.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.bi.dto.*;
import com.xjrsoft.module.bi.entity.VisualDb;
import com.xjrsoft.module.bi.service.IVisualDbService;
import com.xjrsoft.module.bi.vo.VisualDbDetailVo;
import com.xjrsoft.module.bi.vo.VisualDbListVo;
import com.xjrsoft.module.bi.vo.VisualDbPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(GlobalConstant.BI_MODULE_PREFIX + "/db")
@Api(value = GlobalConstant.BI_MODULE_PREFIX + "/db", tags = "BI数据源")
@AllArgsConstructor
public class VisualDbController {

    private final IVisualDbService visualDbService;


    @GetMapping("/info")
    @ApiOperation("获取数据源详情")
    public R info(@RequestParam("id") Long id) {
        VisualDb visualDb = visualDbService.getById(id);
        VisualDbDetailVo visualDbVo = BeanUtil.toBean(visualDb, VisualDbDetailVo.class);
        return R.ok(visualDbVo);
    }


    @GetMapping(value = "/page")
    @ApiOperation(value = "获取数据源数据/(分页)")
    public R page(VisualDbPageDto dto) {
        LambdaQueryWrapper<VisualDb> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(VisualDb.class, x -> VoToColumnUtil.fieldsToColumns(VisualDbPageVo.class).contains(x.getProperty()));
        IPage<VisualDb> page = visualDbService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<VisualDbPageVo> pageOutput = ConventPage.getPageOutput(page, VisualDbPageVo.class);
        return R.ok(pageOutput);
    }

    @PostMapping
    @ApiOperation(value = "新增")
    public R add(@Valid @RequestBody AddVisualDbDto dto) {
        VisualDb visualDb = BeanUtil.toBean(dto, VisualDb.class);
        visualDb.setEnabledMark(1);
        return R.ok(visualDbService.save(visualDb));
    }

    @PutMapping
    @ApiOperation(value = "更新分类")
    public R update(@Valid @RequestBody UpdateVisualDbDto dto) {
        VisualDb visualDb = BeanUtil.toBean(dto, VisualDb.class);
        visualDb.setId(dto.getId());
        return R.ok(visualDbService.updateById(visualDb));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(visualDbService.removeBatchByIds(ids));
    }


    @PostMapping("/db-test")
    @ApiOperation("测试数据源")
    public R testDb(@Valid @RequestBody TestDbDto dto) {
        return R.ok(visualDbService.testConnectionDb(dto));
    }


    @GetMapping(value = "/list")
    @ApiOperation(value = "下拉选择数据源列表(不分页)")
    public R list() {
        List<VisualDb> list = visualDbService.list(Wrappers.lambdaQuery(VisualDb.class)
                .select(VisualDb.class, x -> VoToColumnUtil.fieldsToColumns(VisualDbListVo.class).contains(x.getProperty())));
        List<VisualDbListVo> visualDbListVos = BeanUtil.copyToList(list, VisualDbListVo.class);
        return R.ok(visualDbListVos);
    }


}
