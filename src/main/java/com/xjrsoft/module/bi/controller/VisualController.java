package com.xjrsoft.module.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.bi.dto.*;
import com.xjrsoft.module.bi.entity.Visual;
import com.xjrsoft.module.bi.entity.VisualConfig;
import com.xjrsoft.module.bi.service.IVisualConfigService;
import com.xjrsoft.module.bi.service.IVisualService;
import com.xjrsoft.module.bi.vo.VisualPageVo;
import com.xjrsoft.module.bi.vo.VisualVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.websocket.server.PathParam;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * BI 大屏列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-21
 */
@RestController
@RequestMapping(GlobalConstant.BI_MODULE_PREFIX)
@Api(value = GlobalConstant.BI_MODULE_PREFIX, tags = "BI大屏设计模块")
@AllArgsConstructor
public class VisualController {

    private final IVisualService visualService;

    private final IVisualConfigService visualConfigService;


    @GetMapping(value = "/page")
    @ApiOperation(value = "根据分类配置获取列表数据/(分页)")
    public R page(VisualPageDto dto) {
        LambdaQueryWrapper<Visual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), Visual::getTitle, dto.getKeyword())
                .eq(StrUtil.isNotBlank(dto.getCategory().toString()), Visual::getCategory, dto.getCategory())
                .select(Visual.class, x -> VoToColumnUtil.fieldsToColumns(VisualPageVo.class).contains(x.getProperty()));

        IPage<Visual> page = visualService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<VisualPageVo> pageOutput = ConventPage.getPageOutput(page, VisualPageVo.class);

        return R.ok(pageOutput);
    }


    @GetMapping(value = "/info")
    @ApiOperation("根据id查询模板详情")
    public R info(@Valid @PathParam("id") Long id) {
        VisualVo visualvo = BeanUtil.toBean(visualService.getById(id), VisualVo.class);
        VisualConfig visualCongfig = visualConfigService.getByVisualId(id);
        JSONObject result = new JSONObject();
        result.put("config", visualCongfig);
        result.put("visual", visualvo);
        return R.ok(result);
    }

    @PostMapping
    @ApiOperation(value = "新增Visaul")
    public R add(@Valid @RequestBody AddVisualDto dto) {
        Map<String, Object> result = visualService.add(dto);
        return R.ok(result);
    }


    @PutMapping
    @ApiOperation(value = "更新分类")
    public R update(@Valid @RequestBody UpdateVisualDto dto) {
        Visual visual = BeanUtil.toBean(dto.getVisual(), Visual.class);
        VisualConfig visualConfig = BeanUtil.toBean(dto.getConfig(), VisualConfig.class);
        return R.ok(visualService.updateVisual(dto.getId(), visual, visualConfig));
    }

    @PostMapping("/copy")
    @ApiOperation(value = "拷贝")
    public R copy(@Valid @RequestBody VisualCopyDto dto) {
        Long newVisualId = visualService.copy(dto);
        return R.ok(newVisualId);
    }


    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@RequestBody List<Long> ids) {
        return R.ok(visualService.removeBatchByIds(ids));
    }

}
