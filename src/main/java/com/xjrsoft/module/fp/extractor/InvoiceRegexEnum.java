package com.xjrsoft.module.fp.extractor;

public enum InvoiceRegexEnum {

    /**
     * 机器编码、发票代码、发票号码、开票日期和校验码的提取正则
     */
    REGULAR_A("机器编号:(?<machineNumber>\\d{12})|发票代码:(?<code>\\d{12})|发票号码:(?<number>\\d{8})|:(?<date>\\d{4}年\\d{2}月\\d{2}日)|校验码:(?<checksum>\\d{20}|\\S{4,})"),

    /**
     * 新版发票的机器编码、发票代码、发票号码、开票日期和校验码的提取正则
     */
    REGULAR_A_NEW("发票号码:(?<number>\\d{20})|:(?<date>\\d{4}年\\d{2}月\\d{2}日)|(售名称|销名称):(?<name>\\S*)"),

    /**
     * 发票号码备用提取正则
     */
    REGULAR_A_1("(国制|制普通发票)(?<number>\\d{8})"),

    /**
     * 发票号码跨行提取正则
     */
    REGULAR_A_1R("发票号码:(?<number>\\d{7})[\\s\\S]*?(\\d+)"),

    /**
     * 开票日期备用提取正则
     */
    REGULAR_A_2("开票日期:(?<date>\\d{4}\\d{2}月\\d{2}日)"),

    /**
     * 发票代码备用提取正则
     */
    REGULAR_A_3("发票代码(?<code>\\d{12})"),

    /**
     * 发票代码跨行提取正则
     */
    REGULAR_A_3R("发票代码:(?<code>\\d{10})[\\s\\S]*?(\\d+)"),

    /**
     * 金额、税额提取正则，匹配形如 "合计¥?金额¥?税额" 的文本
     */
    REGULAR_B("合计¥?(?<amount>[^ \\f\\n\\r\\t\\v*]*)(?:¥?(?<taxAmount>\\S*)|\\*+)\\s"),

    /**
     * 金额提取正则，用于匹配结果有误的修正
     */
    REGULAR_BR("合计¥(?<amount>\\d+\\.\\d+)"),

    /**
     * 金额、税额备用提取正则
     */
    REGULAR_B_1("合\\u0020*计\\u0020*¥?(?<amount>[^ ]*)\\u0020+¥?(?:(?<taxAmount>\\S*)|\\*+)\\s"),

    /**
     * 价税合计提取正则，匹配“价税合计（大写）XXX（小写）¥YYY”格式的文本
     */
    REGULAR_C("价税合计\\u0028大写\\u0029(?<amountString>\\S*)\\u0028小写\\u0029¥?(?<amount>\\S*)\\s"),

    /**
     * 收款人、复核、开票人、销售方提取正则，匹配格式为“收款人:xxx复核:xxx开票人:xxx销售方”的字符串
     */
    REGULAR_D("收款人:(?<payee>\\S*)复核:(?<reviewer>\\S*)开票人:(?<drawer>\\S*)销售方"),

    /**
     * 发票类型提取正则，匹配"xxx通发票"格式的发票类型
     */
    REGULAR_E("(?<p>\\S*)通发票"),

    /**
     * 发票类型提取正则，匹配"xxx用发票"格式的发票类型
     */
    REGULAR_E_1("(?<p>\\S*)用发票"),

    /**
     * 发票类型提取 - 辅助正则
     */
    REGULAR_E_AUX("(?:国|统|一|发|票|监|制)"),

    /**
     * 购买方信息提取正则
     */
    REGULAR_F("名称:(?<name>\\S*)|纳税人识别号:(?<code>\\S*)|地址、电话:(?<address>\\S*)|开户行及账号:(?<account>\\S*)|电子支付标识:(?<account2>\\S*)"),

    /**
     * 针对深圳发票的销售方名称提取正则
     */
    REGULAR_FR("名称:(?<name>\\S*)"),

    /**
     * 处理除了金额和税额之外的其他文本元素正则
     */
    REGULAR_G("^(-?\\d+)(\\.\\d+)?$"),

    /**
     * 检查当前详细项字符串是否符合特定条件正则
     */
    REGULAR_H("\\S+\\d*(%|免税|不征税|出口零税率|普通零税率)\\S*"),
    REGULAR_H_1("^ *\\d*(%|免税|不征税|出口零税率|普通零税率)\\S*"),
    REGULAR_H_2("\\S+\\d+%[\\-\\d]+\\S*"),
    REGULAR_H_3("^ *\\d*(%|免税|不征税|出口零税率|普通零税率)\\S*");


    private final String regex;


    InvoiceRegexEnum(String regex) {
        this.regex = regex;
    }


    public String getRegex() {
        return regex;
    }
}

