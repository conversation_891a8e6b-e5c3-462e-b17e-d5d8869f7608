package com.xjrsoft.common.xss;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.AntPathMatcher;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xjrsoft.common.utils.ContextFormDataHolder;
import com.xjrsoft.common.utils.ContextListSchemaHolder;
import com.xjrsoft.common.utils.ZyQqLogHolder;
import com.xjrsoft.module.zy.form.pojo.entity.ZyFormEdit;
import com.xjrsoft.module.zy.form.pojo.entity.ZyQqLog;
import com.xjrsoft.module.zy.form.service.ZyFormEditService;
import com.xjrsoft.module.zy.form.service.ZyQqLogService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.ActiveSpan;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * XSS过滤处理
 *
 * <AUTHOR>
 */
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {
    private static final Logger log = LoggerFactory.getLogger(XssHttpServletRequestWrapper.class);
    /**
     * 没被包装过的HttpServletRequest（特殊场景，需要自己过滤）
     */
    HttpServletRequest orgRequest;

    private final List<String> ignoreXssUrl = ListUtil.toList("/magic-api/**", "/app/func-design/**", "/magic/web/**", "/workflow/**");
    //html过滤
    private final static HTMLFilter HTML_FILTER = new HTMLFilter();

    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        orgRequest = request;
    }

    @Trace
    @Override
    public ServletInputStream getInputStream() throws IOException {

        ZyQqLogHolder.clearContext();
        try {
            ActiveSpan.info("用户:"+StpUtil.getLoginIdAsString());
        } catch (Exception e) {

        }

        String queryString = getQueryString();
        ZyQqLog zyQqLog = new ZyQqLog();
        zyQqLog.setQueryString(queryString);
        zyQqLog.setUrl(orgRequest.getRequestURI());
        if (orgRequest.getRequestURI().indexOf("file") == -1
                && orgRequest.getRequestURI().indexOf("upload") == -1
                && orgRequest.getRequestURI().indexOf("file") == -1
                && orgRequest.getRequestURI().indexOf("export") == -1
                && orgRequest.getRequestURI().indexOf("import") == -1
        ) {
            //form 表单转成 字符串，排除文件
            Map<String, String[]> parameterMap = super.getParameterMap();
            Map<String, String> formMap = new LinkedHashMap<>();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String key = entry.getKey();
                if (entry.getValue().length > 0) {
                    formMap.put(key, StringUtils.join(entry.getValue(), ","));
                } else {
                    formMap.put(key, "");
                }
            }
            zyQqLog.setParam(JSON.toJSONString(formMap));
            try {
                ActiveSpan.info("form:"+JSON.toJSONString(formMap));
            } catch (Exception e) {

            }
        }

        //非json类型，直接返回
        if (!MediaType.APPLICATION_JSON_VALUE.equalsIgnoreCase(super.getHeader(HttpHeaders.CONTENT_TYPE))
                && !MediaType.APPLICATION_JSON_UTF8_VALUE.equalsIgnoreCase(super.getHeader(HttpHeaders.CONTENT_TYPE))
        ) {
            return super.getInputStream();
        }

        //为空，直接返回
        String json = IOUtils.toString(super.getInputStream(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(json)) {
            return super.getInputStream();
        }
        AntPathMatcher matcher = new AntPathMatcher();
        if (ignoreXssUrl.stream().noneMatch(url -> matcher.matchStart(url, orgRequest.getRequestURI()))) {
            //xss过滤 orgRequest.getRequestURI()
//            json = xssEncode(json);
        }
        zyQqLog.setBody(json);
        try {
            log.info("请求参数:" + json);
            ActiveSpan.info("body:"+json);
            new Thread(() -> {
                try {
                    if (orgRequest.getRequestURI().contains("login")
                            || orgRequest.getRequestURI().contains("new-approve")
                            || orgRequest.getRequestURI().contains("xs")
                            || orgRequest.getRequestURI().contains("multi")
                            || orgRequest.getRequestURI().contains("new-launch")
                            || orgRequest.getRequestURI().contains("/form/execute/add")
                            || orgRequest.getRequestURI().contains("/form/execute/update")
                    ){
                        ZyQqLogService zyQqLogService = SpringUtil.getBean(ZyQqLogService.class);
                        zyQqLogService.save(zyQqLog);
                    }

                } catch (Exception e) {

                }
            }).start();
            ZyQqLogHolder.setContext(zyQqLog);
        } catch (Exception e) {

        }

        try {
            JSONObject jsonObject = JSON.parseObject(json);
            if (jsonObject.containsKey("formData")) {
                Object formDataObj = jsonObject.get("formData");
                if (formDataObj instanceof JSONObject) {
                    JSONObject formData = (JSONObject) formDataObj;
                    for (Map.Entry<String, Object> entry : formData.entrySet()) {
                        String key = entry.getKey();
                        if (key.contains("form_")) {
                            String[] strings = key.split("_");
                            ContextFormDataHolder.setContext(strings[1]);
                            ZyFormEditService zyFormEditService = SpringUtil.getBean(ZyFormEditService.class);
                            ZyFormEdit zyFormEdit = zyFormEditService.getById(strings[1]);
                            if (zyFormEdit != null) {
                                ContextListSchemaHolder.setContext(zyFormEdit.getSchemeinfoId());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {

        }

        final ByteArrayInputStream bis = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8));
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return true;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return bis.read();
            }
        };
    }

    @Override
    public String getParameter(String name) {
        String value = super.getParameter(xssEncode(name));
        if (StringUtils.isNotBlank(value)) {
            value = xssEncode(value);
        }
        return value;
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] parameters = super.getParameterValues(name);
        if (parameters == null || parameters.length == 0) {
            return null;
        }

        for (int i = 0; i < parameters.length; i++) {
            parameters[i] = xssEncode(parameters[i]);
        }
        return parameters;
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> map = new LinkedHashMap<>();
        Map<String, String[]> parameters = super.getParameterMap();
        for (String key : parameters.keySet()) {
            String[] values = parameters.get(key);
            for (int i = 0; i < values.length; i++) {
                values[i] = xssEncode(values[i]);
            }
            map.put(key, values);
        }
        return map;
    }

    @Override
    public String getHeader(String name) {
        String value = super.getHeader(xssEncode(name));
        if (StringUtils.isNotBlank(value)) {
            value = xssEncode(value);
        }
        return value;
    }


    private String xssEncode(String input) {
        return HTML_FILTER.filter(input);
    }

    /**
     * 获取最原始的request
     */
    public HttpServletRequest getOrgRequest() {
        return orgRequest;
    }

    /**
     * 获取最原始的request
     */
    public static HttpServletRequest getOrgRequest(HttpServletRequest request) {
        if (request instanceof XssHttpServletRequestWrapper) {
            return ((XssHttpServletRequestWrapper) request).getOrgRequest();
        }

        return request;
    }

}
