
package com.xjrsoft.common.xss;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.xjrsoft.common.utils.*;
import com.xjrsoft.module.workflow.constant.WorkflowConstant;
import com.xjrsoft.module.zy.form.pojo.entity.ZyQqLog;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * XSS过滤
 *
 * <AUTHOR>
 */
public class XssFilter implements Filter {

	@Override
	public void init(FilterConfig config) {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
		ContextFormDataHolder.clearContext();
		ContextListSchemaHolder.clearContext();
		ContextParamMapHolder.clearContext();
		ZyQqLogHolder.clearContext();
		NextTaskInfoUserHolder.clearContext();
		ContextUserHolder.clearContext();
		try {
			ContextUserHolder.setContext(StpUtil.getLoginIdAsString());
		} catch (Exception e) {

		}
		XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(
				(HttpServletRequest) request);

		if (StringUtils.isNotBlank(((HttpServletRequest) request).getQueryString())) {
			ZyQqLog zyQqLog = new ZyQqLog();
			zyQqLog.setQueryString(((HttpServletRequest) request).getQueryString());
			ZyQqLogHolder.setContext(zyQqLog);
			String[] queryStrings = ((HttpServletRequest) request).getQueryString().split("&");
			for (String s : queryStrings) {
				if (s.startsWith("taskId=")) {
					String taskId = s.substring(7);
					HistoryService historyService = SpringUtil.getBean(HistoryService.class);
					HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
					if (task == null) {
						continue;
					}
					List<HistoricVariableInstance> variableInstanceList = historyService.createHistoricVariableInstanceQuery()
							.processInstanceId(task.getProcessInstanceId())
							.variableNameIn(WorkflowConstant.PROCESS_LIST_SCHEMA_NAME_KEY)
							.list();
					Optional<HistoricVariableInstance> listSchema = variableInstanceList.stream().filter(x -> x.getName().equals(WorkflowConstant.PROCESS_LIST_SCHEMA_NAME_KEY)).findFirst();
					if (listSchema.isPresent()) {
						Object value = listSchema.get().getValue();
						if (value != null) {
							ContextListSchemaHolder.setContext(value.toString());
						}
					}
				}
			}
		}
		chain.doFilter(xssRequest, response);
	}

	@Override
	public void destroy() {
	}

}
