package com.xjrsoft.common.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Author: tzx
 * @Date: 2022/3/7 14:52
 */
@Data
@ToString
@ApiModel(value = "Order", description = "排序")
public class Order implements Serializable {

    public Order() {
    }

    @ApiModelProperty(value = "排序字段")
    private String field;

    @ApiModelProperty(value = "排序方式 asc  desc")
    private String order;
}
