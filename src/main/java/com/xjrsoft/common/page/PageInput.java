package com.xjrsoft.common.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * @Author: tzx
 * @Date: 2022/3/7 14:52
 */
@Data
@ToString
@ApiModel(value = "PageInput", description = "分页入参")
public class PageInput implements Serializable {

    public PageInput(){
    }

    @ApiModelProperty(value = "当前页标")
    @Min(value = 1,message = "当前页标必须大于0")
    private Integer limit = 1;

    @ApiModelProperty(value = "每页大小")
    @Min(value = 1,message = "每页大小必须大于0")
    private Integer size = 1000;

    @ApiModelProperty(value = "关键词")
    @Length(max = 50, message = "关键词长度不能超过50")
    private String keyword;

    @ApiModelProperty(value = "排序字段")
    private String field;

    @ApiModelProperty(value = "排序方式 asc  desc")
    private String order;

}
