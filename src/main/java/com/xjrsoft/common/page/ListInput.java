package com.xjrsoft.common.page;

import cn.hutool.core.util.StrUtil;
import com.xjrsoft.common.xss.SQLFilter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * @Author: tzx
 * @Date: 2022/4/28 15:33
 */
@Data
@ToString
@ApiModel(value = "ListInput", description = "不分页入参")
public class ListInput {
    public ListInput(){
    }

    @ApiModelProperty(value = "排序字段")
    private String field;

    @ApiModelProperty(value = "排序方式 asc  desc")
    private String order;

    @ApiModelProperty(value = "关键词")
    @Length(max = 50, message = "关键词长度不能超过50")
    private String keyword;

    public String getField() {
        return SQLFilter.sqlInject(StrUtil.toUnderlineCase(this.field));
    }
}
