package com.xjrsoft.common.sms;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.xjrsoft.common.enums.SmsType;
import com.xjrsoft.config.GlobalConfig;
import com.xjrsoft.config.XjrSmsConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.dromara.sms4j.aliyun.config.AlibabaConfig;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.config.SupplierFactory;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.dromara.sms4j.huawei.config.HuaweiConfig;
import org.dromara.sms4j.provider.enumerate.SupplierType;
import org.dromara.sms4j.tencent.config.TencentConfig;
import org.springframework.stereotype.Component;


@Component
@AllArgsConstructor
public class SmsSender {

    /**
     * 发送验证码
     * @param mobile
     * @param code
     */
    public void sendCaptcha(String mobile, String code) {
        setConfig(SmsType.CAPTCHA);
        SmsFactory.refresh(getSupplierType());
        SmsResponse smsResponse = SmsFactory.createSmsBlend(getSupplierType()).sendMessage(mobile, code);
    }

    /**
     * 发送提醒
     * @param mobile
     * @param text
     */
    public void sendNotify(String mobile, String text) {
        setConfig(SmsType.NOTIFY);
        SmsFactory.refresh(getSupplierType());
        SmsResponse smsResponse = SmsFactory.createSmsBlend(getSupplierType()).sendMessage(mobile, StrUtil.isNotBlank(text) ? text : "");
    }

    /**
     * 发送传阅
     * @param mobile
     * @param text
     */
    public void sendCirculated(String mobile, String text) {
        setConfig(SmsType.CIRCULATED);
        SmsFactory.refresh(getSupplierType());
        SmsResponse smsResponse = SmsFactory.createSmsBlend(getSupplierType()).sendMessage(mobile, StrUtil.isNotBlank(text) ? text : "");
    }

    /**
     * 发送超时
     * @param mobile
     * @param text
     */
    public void sendTimeout(String mobile, String text) {
        setConfig(SmsType.TIMEOUT);
        SmsFactory.refresh(getSupplierType());
        SmsResponse smsResponse = SmsFactory.createSmsBlend(getSupplierType()).sendMessage(mobile, StrUtil.isNotBlank(text) ? text : "");
    }



    private SupplierType getSupplierType() {
        XjrSmsConfig xjrSmsConfig = SpringUtil.getBean(XjrSmsConfig.class);
        switch (xjrSmsConfig.getPlatform()) {
            case ALI_CLOUD:
                return SupplierType.ALIBABA;
            case TENCENT_CLOUD:
                return SupplierType.TENCENT;
            case HW_CLOUD:
                return SupplierType.HUAWEI;
            case HEYI_CLOUD:
                return SupplierType.UNI_SMS;
            case JD_CLOUD:
                return SupplierType.JD_CLOUD;
            case RONGLIAN_CLOUD:
                return SupplierType.CLOOPEN;
            case YIMEI_CLOUD:
                return SupplierType.EMAY;
            case TIANYI_CLOUD:
                return SupplierType.CTYUN;
            case YUNPIAN_CLOUD:
                return SupplierType.YUNPIAN;
            default:
                throw new RuntimeException("未配置短信服务供应商信息: xjrsoft.sms");
        }
    }

    private void setConfig(SmsType type) {
        XjrSmsConfig xjrSmsConfig = SpringUtil.getBean(XjrSmsConfig.class);

        switch (xjrSmsConfig.getPlatform()) {
            case ALI_CLOUD:
                AlibabaConfig alibabaConfig = SupplierFactory.getAlibabaConfig();
                break;

            case TENCENT_CLOUD:
                TencentConfig tencentConfig = SupplierFactory.getTencentConfig();
                break;
            case HW_CLOUD:
                if(type == SmsType.CAPTCHA){
                    HuaweiConfig huaweiConfig = SupplierFactory.getHuaweiConfig();
                    huaweiConfig.setSender(xjrSmsConfig.getCaptchaSender());
                    huaweiConfig.setTemplateId(xjrSmsConfig.getCaptchaTemplateId());
                }
                if(type == SmsType.NOTIFY){
                    HuaweiConfig huaweiConfig = SupplierFactory.getHuaweiConfig();
                    huaweiConfig.setSender(xjrSmsConfig.getNotifySender());
                    huaweiConfig.setTemplateId(xjrSmsConfig.getNotifyTemplateId());
                }
                if(type == SmsType.CIRCULATED){
                    HuaweiConfig huaweiConfig = SupplierFactory.getHuaweiConfig();
                    huaweiConfig.setSender(xjrSmsConfig.getCirculatedSender());
                    huaweiConfig.setTemplateId(xjrSmsConfig.getCirculatedTemplateId());
                }
                if(type == SmsType.TIMEOUT){
                    HuaweiConfig huaweiConfig = SupplierFactory.getHuaweiConfig();
                    huaweiConfig.setSender(xjrSmsConfig.getTimeoutSender());
                    huaweiConfig.setTemplateId(xjrSmsConfig.getTimeoutTemplateId());
                }


                break;
            case HEYI_CLOUD:
                SupplierFactory.getUniConfig();
                break;
            case JD_CLOUD:
                SupplierFactory.getJdCloudConfig();
                break;
            case RONGLIAN_CLOUD:
                SupplierFactory.getCloopenConfig();
                break;
            case YIMEI_CLOUD:
                SupplierFactory.getEmayConfig();
                break;
            case TIANYI_CLOUD:
                SupplierFactory.getCtyunConfig();
                break;
            case YUNPIAN_CLOUD:
                SupplierFactory.getYunpianConfig();
                break;
            default:
                throw new RuntimeException("未配置短信服务供应商信息: xjrsoft.sms");
        }
    }

}
