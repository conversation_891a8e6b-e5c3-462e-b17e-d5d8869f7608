package com.xjrsoft.common.enums;


/**
 * <AUTHOR>
 */
public enum AuthorizeType {
    /**
     * 菜单
     */
    MENU(0, "菜单"),
    /**
     * 按钮
     */
    BUTTON(1, "按钮"),
    /**
     * 列表
     */
    COLUMN(2, "列表"),
    /**
     * 表单
     */
    FORM(3, "表单");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    AuthorizeType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
