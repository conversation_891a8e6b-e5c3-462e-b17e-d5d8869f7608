package com.xjrsoft.common.enums;

/**
 * typescript 字段类型美剧
 * @Author: tzx
 * @Date: 2022/5/27 9:44
 */
public enum TsColumnType {
    /**
     * 字符串
     */
    STRING(0, "字符串"),
    /**
     * 数字
     */
    NUMBER(1, "数字"),
    /**
     * 布尔
     */
    BOOL(2, "布尔");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    TsColumnType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
