package com.xjrsoft.common.enums;

import com.baomidou.mybatisplus.annotation.DbType;

/**
 * @Author: tzx
 * @Date: 2022/5/5 19:07
 */
public enum DbFieldsType {
    /**
     * 短文本
     * */
    VARCHAR(0, "短文本"),

    /**
     * 阿里云
     * */
    VARCHARMAX(1, "长文本"),

    /**
     * 数字类型
     * */
    INT(2, "数字"),

    /**
     * 小数
     * */
    FLOAT(3, "小数"),

    /**
     * 日期
     * */
    DATE(4, "日期"),

    /**
     * 日期时间类型
     * */
    DATETIME(5, "日期时间"),
    /**
     * 主表主键
     * */
    FK(6, "主表主键"),
    /**
     * 长整数
     * */
    LONG(7, "长整数"),
    /**
     * 时间
     * */
    TIME(8, "时间");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public static DbFieldsType getFieldType(Integer fieldType) {
        DbFieldsType[] var1 = values();
        int var2 = var1.length;

        for (DbFieldsType type : var1) {
            if (type.code == fieldType) {
                return type;
            }
        }

        return VARCHAR;
    }

    DbFieldsType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
