package com.xjrsoft.common.enums;

/**
 * @Author: tzx
 * @Date: 2022/12/16 10:06
 */
public enum YesOrNoEnum {
    /**
     * 值
     */
    NO(0, "否"),

    /**
     * API
     */
    YES(1, "是");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    YesOrNoEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
