package com.xjrsoft.common.enums;

/**
 * @Author: tzx
 * @Date: 2022/3/4 17:04
 */
public enum DeleteMark {
    /*
     *  未启用
     * */
    NODELETE(0, "未删除"),
    /*
     *  启用
     * */
    DELETED(1, "已删除");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    DeleteMark(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
