package com.xjrsoft.common.enums;

/**
 * @Author: tzx
 * @Date: 2022/3/3 15:49
 */
public enum ResponseCode {

    /*
    * 成功
    * */
    SUCCESS(0, "操作成功"),
    /*
     * 业务异常
     * */
    FAILURE(10400, "业务异常"),
    /*
     * 请求未授权
     * */
    UN_AUTHORIZED(10401, "请求未授权"),
    /*
     * 404 没找到资源
     * */
    NOT_FOUND(10404, "404 没找到资源"),
    /*
     * 消息不能读取
     * */
    MSG_NOT_READABLE(10400, "消息不能读取"),
    /*
     * 不支持当前请求方法
     * */
    METHOD_NOT_SUPPORTED(10405, "不支持当前请求方法"),
    /*
     * 不支持当前媒体类型
     * */
    MEDIA_TYPE_NOT_SUPPORTED(10415, "不支持当前媒体类型"),
    /*
     * 请求被拒绝
     * */
    REQ_REJECT(10403, "请求被拒绝"),
    /*
     * 服务器异常
     * */
    INTERNAL_SERVER_ERROR(10500, "服务器异常"),
    /*
     * 缺少必要的请求参数
     * */
    PARAM_MISS(10400, "缺少必要的请求参数"),
    /*
     * 请求参数类型错误
     * */
    PARAM_TYPE_ERROR(10400, "请求参数类型错误"),
    /*
     * 请求参数绑定错误
     * */
    PARAM_BIND_ERROR(10400, "请求参数绑定错误"),
    /*
     * 参数校验失败
     * */
    PARAM_VALID_ERROR(10400, "参数校验失败"),
    /*
     * 参数校验失败
     * */
    MAGIC_API_UN_AUTHORIZED(10404, "magicAPI接口未授权！");

    final int code;
    final String message;

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    ResponseCode(final int code, final String message) {
        this.code = code;
        this.message = message;
    }
}
