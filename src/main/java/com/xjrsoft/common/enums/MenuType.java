package com.xjrsoft.common.enums;

/**
 * @Author: tzx
 * @Date: 2022/8/17 15:00
 */
public enum MenuType {
    /**
     * 字符串
     */
    MENU(0, "菜单"),
    /**
     * 数字
     */
    FUNCTION(1, "功能");

    final int code;
    final String value;


    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    MenuType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
