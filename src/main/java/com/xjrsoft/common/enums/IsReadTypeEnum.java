package com.xjrsoft.common.enums;

public enum IsReadTypeEnum {
    /**
     * 未读
     */
    UNREAD(0, "未读"),
    /**
     * 已读
     */
    READ(1, "已读");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    IsReadTypeEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }



}
