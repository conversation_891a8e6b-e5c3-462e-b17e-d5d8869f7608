package com.xjrsoft.common.enums;

/**
 * @Author: tzx
 * @Date: 2022/3/3 16:37
 */
public enum  LogCategory {

    /**
     * 登录
     * */
    LOGIN(1, "登录"),

    /**
     * 登录
     * */
    GET(2, "访问"),

    /**
     * 登录
     * */
    OPERAT(3, "操作"),

    /**
     * 登录
     * */
    EXCEPTION(4, "异常");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    LogCategory(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
