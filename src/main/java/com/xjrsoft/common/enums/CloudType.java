package com.xjrsoft.common.enums;

/**
 * @Author: tzx
 * @Date: 2022/3/8 9:15
 */
public enum CloudType {
    /**
     * 七牛云
     * */
    QINIUCLOUD(1, "七牛云"),

    /**
     * 阿里云
     * */
    ALICLOUD(2, "阿里云"),

    /**
     * 腾讯云
     * */
    QCLOUD(3, "腾讯云"),

    /**
     * 华为云
     * */
    HWCLOUD(4, "华为云"),

    /**
     * Minio
     * */
    MINIO(5, "Minio");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    CloudType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
