package com.xjrsoft.common.enums;

/**
 * @Author: tzx
 * @Date: 2023/12/14 14:04
 */
public enum ErrorTypeEnum {
    /**
     * 系统错误信息
     * */
    SYSTEM(0, "系统错误信息"),

    /**
     * 友好错误信息
     * */
    SIMPLE(1, "友好错误信息");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    ErrorTypeEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }

}
