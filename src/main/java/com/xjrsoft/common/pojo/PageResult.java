package com.xjrsoft.common.pojo;


import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
public final class PageResult<T> implements Serializable {


    private List<T> list;


    private Integer total;
    private Integer pageSize;

    public PageResult() {
    }

    public PageResult(List<T> list, Integer total) {
        this.list = list;
        this.total = total;
    }
    public PageResult(List<T> list, Integer total,Integer pageSize) {
        this.list = list;
        this.total = total;
        this.pageSize = pageSize;
    }

    public PageResult(Integer total) {
        this.list = new ArrayList<>();
        this.total = total;
    }

    public static <T> PageResult<T> empty() {
        return new PageResult<>(0);
    }

    public static <T> PageResult<T> empty(Integer total) {
        return new PageResult<>(total);
    }

}
