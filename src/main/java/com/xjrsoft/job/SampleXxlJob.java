package com.xjrsoft.job;

import cn.hutool.db.Db;
import com.xjrsoft.common.utils.DatasourceUtil;
import com.xjrsoft.module.generator.utils.SqlUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import javax.sql.DataSource;
import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Log4j2
@Component
public class SampleXxlJob {

    @Autowired
    MagicAPIService service;

    @XxlJob("api")
    public void api() throws Exception {
        try {
            Map<String, Object> params = new HashMap<>();
            String param = XxlJobHelper.getJobParam();
            Object execute = service.execute("GET", param, params);
            log.info("api执行结果：{}", execute);
        } catch (Exception e) {
            log.error("api执行异常", e);
        }

    }
    @XxlJob("sql")
    public void sql() throws Exception {
        try {
            String param = XxlJobHelper.getJobParam();
            DataSource datasource = DatasourceUtil.getDataSource("master");
            Db use = Db.use(datasource);
            use.execute(param);
        } catch (SQLException e) {
            log.error("sql执行异常", e);
        }
    }
}

