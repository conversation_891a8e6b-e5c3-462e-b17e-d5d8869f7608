server:
  port: 1999
config:
  db:
    ip: *************
    port: 1521
    username: yf0513
    password: yf0513
    im:
      ip: ***************
      port: 13306
      username: root
      password: 369852
  #redis
  redis:
    ip: ***************
    port: 16379
  #minio
  oss:
    ip: ***************
    port: 9005
xxl:
  job:
    accessToken: default_token
    admin:
     addresses: http://127.0.0.1:58080/xxl-job-admin
    executor:
      appname: xxl-job-executor-sample
      address:
      ip: 127.0.0.1
      port: 9999
      logretentiondays: 30
      logpath: ./xxl-job/logs
pdf:
  url: http://localhost:3999/getPdf
  searchKeyword: http://localhost:3999/searchKeyword
  stampPdf: http://localhost:3999/stampPdf
