{"msg": "", "code": 0, "data": {"formInfos": [{"formType": 1, "formJson": "{\"databaseId\":\"master\",\"tableStructureConfigs\":[{\"operator\":4,\"tableName\":\"CASE_ERP_APPLY\",\"tableComment\":null,\"isMain\":true,\"pkField\":null,\"tableFieldConfigs\":[{\"fieldName\":\"APPLY_USER_IDS\",\"fieldType\":null,\"fieldLength\":50,\"fieldComment\":\"密码框\"},{\"fieldName\":\"APPLY_DEP_NAME\",\"fieldType\":null,\"fieldLength\":50,\"fieldComment\":\"id\"},{\"fieldName\":\"APPLY_NUMBER\",\"fieldType\":null,\"fieldLength\":50,\"fieldComment\":\"姓名\"},{\"fieldName\":\"THEME\",\"fieldType\":null,\"fieldLength\":50,\"fieldComment\":\"密码\"},{\"fieldName\":\"REMARK\",\"fieldType\":null,\"fieldLength\":50,\"fieldComment\":\"单行文本1\"},{\"fieldName\":\"FILE_PATH\",\"fieldType\":null,\"fieldLength\":50,\"fieldComment\":\"多行文本\"},{\"fieldName\":\"APPLY_DEP_ID\",\"fieldType\":null,\"fieldLength\":50,\"fieldComment\":\"计数组件\"}]}],\"formEventConfig\":{\"0\":[{\"type\":\"circle\",\"color\":\"#2774ff\",\"text\":\"开始节点\",\"icon\":\"#icon-kaishi\",\"bgcColor\":\"#D8E5FF\",\"isUserDefined\":false},{\"color\":\"#F6AB01\",\"icon\":\"#icon-chushihua\",\"text\":\"初始化表单\",\"bgcColor\":\"#f9f5ea\",\"isUserDefined\":false,\"nodeInfo\":{\"processEvent\":[]}}],\"1\":[{\"color\":\"#B36EDB\",\"icon\":\"#icon-shujufenxi\",\"text\":\"获取表单数据\",\"detail\":\"(新增无此操作)\",\"bgcColor\":\"#F8F2FC\",\"isUserDefined\":false,\"nodeInfo\":{\"processEvent\":[]}}],\"2\":[{\"color\":\"#F8625C\",\"icon\":\"#icon-jiazai\",\"text\":\"加载表单\",\"bgcColor\":\"#FFF1F1\",\"isUserDefined\":false,\"nodeInfo\":{\"processEvent\":[]}}],\"3\":[{\"color\":\"#6C6AE0\",\"icon\":\"#icon-jsontijiao\",\"text\":\"提交表单\",\"bgcColor\":\"#F5F4FF\",\"isUserDefined\":false,\"nodeInfo\":{\"processEvent\":[]}}],\"4\":[{\"type\":\"circle\",\"color\":\"#F8625C\",\"text\":\"结束节点\",\"icon\":\"#icon-jieshuzhiliao\",\"bgcColor\":\"#FFD6D6\",\"isLast\":true,\"isUserDefined\":false}]},\"tableConfigs\":[{\"tableName\":\"CASE_ERP_APPLY\",\"isMain\":true,\"pkField\":\"ID\",\"pkType\":\"String\",\"relationField\":null,\"relationTableField\":null}],\"formJson\":{\"list\":[{\"title\":\"新建标签页\",\"auditMode\":true,\"hidden\":false,\"key\":\"1afa4f5976dd488f8b9c4d221164db92\",\"formula\":[],\"list\":[{\"label\":\"密码框\",\"type\":\"password\",\"key\":\"a123996b4fda48f3ba31b292d8ab7abc\",\"bindTable\":\"CASE_ERP_APPLY\",\"bindField\":\"APPLY_USER_IDS\",\"rules\":[],\"children\":null,\"layout\":null,\"options\":{\"width\":\"100%\",\"span\":\"\",\"defaultValue\":\"\",\"placeholder\":\"请输入密码框\",\"maxlength\":null,\"prefix\":\"\",\"addonBefore\":\"\",\"addonAfter\":\"\",\"visibilityToggle\":true,\"disabled\":false,\"allowClear\":false,\"showLabel\":true,\"required\":false,\"isShow\":true,\"rules\":[],\"events\":{},\"formula\":[]},\"bindStartTime\":null,\"bindEndTime\":null,\"value\":null,\"code\":null,\"isSubFormChild\":false,\"height\":null,\"position\":null,\"width\":null,\"colspan\":null,\"rowspan\":null,\"class\":null}],\"hiddenComponent\":[]},{\"title\":\"基础数据\",\"auditMode\":true,\"hidden\":false,\"key\":\"078f6fea74a64edbae0faa34d2cbc65e\",\"formula\":[],\"list\":[{\"label\":\"id\",\"type\":\"textarea\",\"key\":\"cc2d550b81b84cefb5d762af23be9abd\",\"bindTable\":\"CASE_ERP_APPLY\",\"bindField\":\"APPLY_DEP_NAME\",\"rules\":[],\"children\":null,\"layout\":null,\"options\":{\"width\":\"100%\",\"span\":\"\",\"defaultValue\":\"\",\"placeholder\":\"请输入id\",\"maxlength\":null,\"rows\":4,\"autoSize\":false,\"showCount\":false,\"disabled\":false,\"showLabel\":true,\"allowClear\":false,\"required\":false,\"isShow\":true,\"rules\":[],\"events\":{},\"formula\":[]},\"bindStartTime\":null,\"bindEndTime\":null,\"value\":null,\"code\":null,\"isSubFormChild\":false,\"height\":null,\"position\":null,\"width\":null,\"colspan\":null,\"rowspan\":null,\"class\":null},{\"label\":\"姓名\",\"type\":\"input\",\"key\":\"1bd03aaf9afe42a29b374216ce0a87db\",\"bindTable\":\"CASE_ERP_APPLY\",\"bindField\":\"APPLY_NUMBER\",\"rules\":[],\"children\":null,\"layout\":null,\"options\":{\"width\":\"100%\",\"span\":\"\",\"defaultValue\":\"\",\"placeholder\":\"请输入姓名\",\"maxlength\":null,\"prefix\":\"\",\"suffix\":\"\",\"addonBefore\":\"\",\"addonAfter\":\"\",\"disabled\":false,\"allowClear\":false,\"showLabel\":true,\"required\":false,\"rules\":[],\"events\":{},\"isSave\":false,\"isShow\":true,\"scan\":false,\"formula\":[]},\"bindStartTime\":null,\"bindEndTime\":null,\"value\":null,\"code\":null,\"isSubFormChild\":false,\"height\":null,\"position\":null,\"width\":null,\"colspan\":null,\"rowspan\":null,\"class\":null},{\"label\":\"密码\",\"type\":\"password\",\"key\":\"40602abf92f24613b91c60e3d9418c12\",\"bindTable\":\"CASE_ERP_APPLY\",\"bindField\":\"THEME\",\"rules\":[],\"children\":null,\"layout\":null,\"options\":{\"width\":\"100%\",\"span\":\"\",\"defaultValue\":\"\",\"placeholder\":\"请输入密码\",\"maxlength\":null,\"prefix\":\"\",\"addonBefore\":\"\",\"addonAfter\":\"\",\"visibilityToggle\":true,\"disabled\":false,\"allowClear\":false,\"showLabel\":true,\"required\":false,\"isShow\":true,\"rules\":[],\"events\":{},\"formula\":[]},\"bindStartTime\":null,\"bindEndTime\":null,\"value\":null,\"code\":null,\"isSubFormChild\":false,\"height\":null,\"position\":null,\"width\":null,\"colspan\":null,\"rowspan\":null,\"class\":null}],\"hiddenComponent\":[]},{\"title\":\"新建标签页\",\"auditMode\":false,\"hidden\":false,\"key\":\"0928b2fb7ca449b497860e0aa3d18872\",\"formula\":[],\"list\":[{\"label\":\"单行文本1\",\"type\":\"input-cs\",\"key\":\"446fe70b255a43c492757b761fe2c59a\",\"bindTable\":\"CASE_ERP_APPLY\",\"bindField\":\"REMARK\",\"rules\":[],\"children\":null,\"layout\":null,\"options\":{\"width\":\"100%\",\"span\":\"\",\"defaultValue\":\"\",\"placeholder\":\"请输入单行文本\",\"maxlength\":null,\"prefix\":\"\",\"suffix\":\"\",\"addonBefore\":\"\",\"addonAfter\":\"\",\"disabled\":false,\"allowClear\":false,\"showLabel\":true,\"required\":false,\"rules\":[],\"events\":{},\"isSave\":false,\"isShow\":true,\"scan\":false,\"formula\":[]},\"bindStartTime\":null,\"bindEndTime\":null,\"value\":null,\"code\":null,\"isSubFormChild\":false,\"height\":null,\"position\":null,\"width\":null,\"colspan\":null,\"rowspan\":null,\"class\":null}],\"hiddenComponent\":[]},{\"title\":\"新建标签3333页\",\"auditMode\":false,\"hidden\":false,\"key\":\"0a9ed6ae73724ccfb2df5fe47cfec4d1\",\"formula\":[],\"list\":[{\"label\":\"多行文本\",\"type\":\"textarea\",\"key\":\"0f4ec3872af54bebbebbe64954131428\",\"bindTable\":\"CASE_ERP_APPLY\",\"bindField\":\"FILE_PATH\",\"rules\":[],\"children\":null,\"layout\":null,\"options\":{\"width\":\"100%\",\"span\":\"\",\"defaultValue\":\"1\",\"placeholder\":\"请输入多行文本\",\"maxlength\":null,\"rows\":4,\"autoSize\":false,\"showCount\":false,\"disabled\":false,\"showLabel\":true,\"allowClear\":false,\"required\":false,\"isShow\":true,\"rules\":[],\"events\":{},\"formula\":[]},\"bindStartTime\":null,\"bindEndTime\":null,\"value\":null,\"code\":null,\"isSubFormChild\":false,\"height\":null,\"position\":null,\"width\":null,\"colspan\":null,\"rowspan\":null,\"class\":null}],\"hiddenComponent\":[]},{\"title\":\"新建标3332121签页\",\"auditMode\":false,\"hidden\":false,\"key\":\"660952983360445b9c49b2339028c53c\",\"formula\":[],\"list\":[{\"label\":\"计数组件\",\"type\":\"number\",\"key\":\"fe86f655d7fa498487d80ed66e385cb2\",\"bindTable\":\"CASE_ERP_APPLY\",\"bindField\":\"APPLY_DEP_ID\",\"rules\":[],\"children\":null,\"layout\":null,\"options\":{\"width\":\"100%\",\"span\":\"\",\"defaultValue\":0,\"min\":0,\"max\":100,\"step\":1,\"maxlength\":null,\"disabled\":false,\"showLabel\":true,\"controls\":true,\"required\":false,\"subTotal\":false,\"isShow\":true,\"rules\":[],\"events\":{},\"formula\":[]},\"bindStartTime\":null,\"bindEndTime\":null,\"value\":null,\"code\":null,\"isSubFormChild\":false,\"height\":null,\"position\":null,\"width\":null,\"colspan\":null,\"rowspan\":null,\"class\":null}],\"hiddenComponent\":[]}],\"config\":{\"formType\":\"modal\",\"size\":\"default\",\"hideRequiredMark\":null,\"layout\":\"horizontal\",\"labelAlign\":\"right\",\"labelCol\":{\"span\":3,\"offset\":0},\"formWidth\":900}},\"isDataAuth\":false,\"dataAuthList\":[]}", "formConfig": {"key": "form_1788462521134452737_1716025688806", "formType": 1, "formId": "1788462521134452737", "formName": "编辑视图参数测速", "showChildren": true, "requiredAll": true, "viewAll": true, "editAll": true, "children": [{"key": "a123996b4fda48f3ba31b292d8ab7abc", "required": true, "view": true, "edit": true, "disabled": false, "tableName": "", "fieldName": "密码框", "fieldId": "APPLY_USER_IDS", "isSubTable": false, "showChildren": true, "children": []}, {"key": "cc2d550b81b84cefb5d762af23be9abd", "required": true, "view": true, "edit": true, "disabled": false, "tableName": "", "fieldName": "id", "fieldId": "APPLY_DEP_NAME", "isSubTable": false, "showChildren": true, "children": []}, {"key": "1bd03aaf9afe42a29b374216ce0a87db", "required": true, "view": true, "edit": true, "disabled": false, "tableName": "", "fieldName": "姓名", "fieldId": "APPLY_NUMBER", "isSubTable": false, "showChildren": true, "children": []}, {"key": "40602abf92f24613b91c60e3d9418c12", "required": true, "view": true, "edit": true, "disabled": false, "tableName": "", "fieldName": "密码", "fieldId": "THEME", "isSubTable": false, "showChildren": true, "children": []}, {"key": "446fe70b255a43c492757b761fe2c59a", "required": true, "view": true, "edit": true, "disabled": false, "tableName": "", "fieldName": "单行文本1", "fieldId": "REMARK", "isSubTable": false, "showChildren": true, "children": []}, {"key": "0f4ec3872af54bebbebbe64954131428", "required": true, "view": true, "edit": true, "disabled": false, "tableName": "", "fieldName": "多行文本", "fieldId": "FILE_PATH", "isSubTable": false, "showChildren": true, "children": []}, {"key": "fe86f655d7fa498487d80ed66e385cb2", "required": true, "view": true, "edit": true, "disabled": false, "tableName": "", "fieldName": "计数组件", "fieldId": "APPLY_DEP_ID", "isSubTable": false, "showChildren": true, "children": []}]}, "functionalModule": null, "functionName": null, "functionFormName": null}], "schemaInfo": {"id": "1789907236182659073", "code": "2024", "name": "新增测试模板", "category": "1630814561619472386", "deploymentId": "c4ee19fa-14fb-11ef-a9f3-988fe06f0e40", "definitionId": "process_jiun5e7f:20:c4f9167c-14fb-11ef-a9f3-988fe06f0e40", "appShow": 0, "remark": null, "xmlContent": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<bpmn:definitions xmlns:bpmn=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:camunda=\"http://camunda.org/schema/1.0/bpmn\" xmlns:modeler=\"http://camunda.org/schema/modeler/1.0\" id=\"Definitions_1u51epq\" targetNamespace=\"http://bpmn.io/schema/bpmn\" exporter=\"Camunda Modeler\" exporterVersion=\"5.2.0\" modeler:executionPlatform=\"Camunda Platform\" modeler:executionPlatformVersion=\"7.17.0\">\n  <bpmn:process id=\"process_jiun5e7f\" name=\"process_jiun5e7f\" isExecutable=\"true\">\n    <bpmn:startEvent id=\"Event_start_node\" name=\"开始节点\">\n      <bpmn:outgoing>Flow_0q8p2ki</bpmn:outgoing>\n    </bpmn:startEvent>\n    <bpmn:userTask id=\"Activity_0eu2df5\" name=\"发起审批\">\n      <bpmn:incoming>Flow_0q8p2ki</bpmn:incoming>\n      <bpmn:outgoing>Flow_17atnar</bpmn:outgoing>\n    </bpmn:userTask>\n    <bpmn:sequenceFlow id=\"Flow_0q8p2ki\" name=\"流程线\" sourceRef=\"Event_start_node\" targetRef=\"Activity_0eu2df5\" />\n    <bpmn:userTask id=\"Activity_0qnb6yk\" name=\"领导审批\">\n      <bpmn:incoming>Flow_17atnar</bpmn:incoming>\n      <bpmn:outgoing>Flow_0sjhs4f</bpmn:outgoing>\n    </bpmn:userTask>\n    <bpmn:sequenceFlow id=\"Flow_17atnar\" name=\"流程线\" sourceRef=\"Activity_0eu2df5\" targetRef=\"Activity_0qnb6yk\" />\n    <bpmn:endEvent id=\"Event_07q1c4l\" name=\"结束节点\">\n      <bpmn:incoming>Flow_0sjhs4f</bpmn:incoming>\n    </bpmn:endEvent>\n    <bpmn:sequenceFlow id=\"Flow_0sjhs4f\" name=\"流程线\" sourceRef=\"Activity_0qnb6yk\" targetRef=\"Event_07q1c4l\" />\n  </bpmn:process>\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_1\">\n    <bpmndi:BPMNPlane id=\"BPMNPlane_1\" bpmnElement=\"process_jiun5e7f\">\n      <bpmndi:BPMNShape id=\"Event_start_node_di\" bpmnElement=\"Event_start_node\">\n        <dc:Bounds x=\"159\" y=\"359\" width=\"36\" height=\"36\" />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_0eu2df5_di\" bpmnElement=\"Activity_0eu2df5\">\n        <dc:Bounds x=\"300\" y=\"320\" width=\"100\" height=\"80\" />\n        <bpmndi:BPMNLabel />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Activity_0qnb6yk_di\" bpmnElement=\"Activity_0qnb6yk\">\n        <dc:Bounds x=\"510\" y=\"330\" width=\"100\" height=\"80\" />\n        <bpmndi:BPMNLabel />\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNShape id=\"Event_07q1c4l_di\" bpmnElement=\"Event_07q1c4l\">\n        <dc:Bounds x=\"722\" y=\"352\" width=\"36\" height=\"36\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"718\" y=\"395\" width=\"44\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNShape>\n      <bpmndi:BPMNEdge id=\"Flow_0q8p2ki_di\" bpmnElement=\"Flow_0q8p2ki\">\n        <di:waypoint x=\"195\" y=\"377\" />\n        <di:waypoint x=\"248\" y=\"377\" />\n        <di:waypoint x=\"248\" y=\"360\" />\n        <di:waypoint x=\"300\" y=\"360\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"247\" y=\"361\" width=\"33\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_17atnar_di\" bpmnElement=\"Flow_17atnar\">\n        <di:waypoint x=\"400\" y=\"360\" />\n        <di:waypoint x=\"455\" y=\"360\" />\n        <di:waypoint x=\"455\" y=\"370\" />\n        <di:waypoint x=\"510\" y=\"370\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"411\" y=\"342\" width=\"33\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNEdge>\n      <bpmndi:BPMNEdge id=\"Flow_0sjhs4f_di\" bpmnElement=\"Flow_0sjhs4f\">\n        <di:waypoint x=\"610\" y=\"370\" />\n        <di:waypoint x=\"722\" y=\"370\" />\n        <bpmndi:BPMNLabel>\n          <dc:Bounds x=\"650\" y=\"352\" width=\"33\" height=\"14\" />\n        </bpmndi:BPMNLabel>\n      </bpmndi:BPMNEdge>\n    </bpmndi:BPMNPlane>\n  </bpmndi:BPMNDiagram>\n</bpmn:definitions>", "jsonContent": "{\"processConfig\":{\"processId\":\"process_jiun5e7f\",\"code\":\"2024\",\"name\":\"新增测试模板\",\"category\":\"1630814561619472386\",\"nameRule\":\"\",\"nameRuleConfigs\":[],\"autoAgreeRule\":[],\"isPrevChooseNext\":0,\"noHandler\":0,\"implStartNodeForm\":null,\"appShow\":false,\"defaultFormList\":[{\"key\":\"form_1788462521134452737_1716025688806\",\"formType\":1,\"formId\":\"1788462521134452737\",\"formName\":\"编辑视图参数测速\",\"showChildren\":null,\"requiredAll\":null,\"viewAll\":null,\"editAll\":null,\"children\":null}],\"authConfig\":{\"authType\":0,\"authMemberConfigs\":[]},\"menuConfig\":{\"code\":\"\",\"name\":\"\",\"parentId\":null,\"icon\":\"\",\"sortCode\":null,\"remark\":\"\"},\"formInitConfig\":{\"enabled\":false,\"formType\":1,\"formId\":null,\"formName\":\"\"},\"timeoutRemidConfig\":{\"enabled\":false,\"hour\":null,\"interval\":null,\"pushHits\":1,\"pushMemberConfigs\":[]},\"relationProcessConfigs\":[],\"processParamConfigs\":[],\"remark\":\"\",\"xmlContent\":\"\",\"workflowChat\":\"http://202.168.189.182:19000/xjrsoft/20240518/7fcffe3490ec4ac89f70c3cf5a6ea57f.svg\"},\"childNodeConfig\":[{\"id\":\"Event_start_node\",\"type\":\"bpmn:StartEvent\",\"name\":\"开始节点\",\"parentId\":\"process_jiun5e7f\",\"remark\":\"\",\"formConfigs\":[{\"key\":\"form_1788462521134452737_1716025688806\",\"formType\":1,\"formId\":\"1788462521134452737\",\"formName\":\"编辑视图参数测速\",\"name\":\"编辑视图参数测速\",\"showChildren\":true,\"requiredAll\":true,\"viewAll\":true,\"editAll\":true,\"children\":[{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"密码框\",\"fieldId\":\"APPLY_USER_IDS\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"password\",\"key\":\"a123996b4fda48f3ba31b292d8ab7abc\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"id\",\"fieldId\":\"APPLY_DEP_NAME\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"textarea\",\"key\":\"cc2d550b81b84cefb5d762af23be9abd\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"姓名\",\"fieldId\":\"APPLY_NUMBER\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"input\",\"key\":\"1bd03aaf9afe42a29b374216ce0a87db\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"密码\",\"fieldId\":\"THEME\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"password\",\"key\":\"40602abf92f24613b91c60e3d9418c12\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"单行文本1\",\"fieldId\":\"REMARK\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"input-cs\",\"key\":\"446fe70b255a43c492757b761fe2c59a\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"多行文本\",\"fieldId\":\"FILE_PATH\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"textarea\",\"key\":\"0f4ec3872af54bebbebbe64954131428\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"计数组件\",\"fieldId\":\"APPLY_DEP_ID\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"number\",\"key\":\"fe86f655d7fa498487d80ed66e385cb2\",\"children\":[]}]}],\"subProcessInitiator\":\"\",\"assignmentConfig\":{\"formAssignmentConfigs\":[],\"paramAssignmentConfigs\":[]},\"startEventConfigs\":[],\"endEventConfigs\":[],\"status\":\"0\"},{\"id\":\"Activity_0eu2df5\",\"type\":\"bpmn:UserTask\",\"name\":\"发起审批\",\"parentId\":\"process_jiun5e7f\",\"remark\":\"\",\"formConfigs\":[{\"key\":\"form_1788462521134452737_1716025688806\",\"formType\":1,\"formId\":\"1788462521134452737\",\"formName\":\"编辑视图参数测速\",\"name\":\"编辑视图参数测速\",\"showChildren\":true,\"requiredAll\":true,\"viewAll\":true,\"editAll\":true,\"children\":[{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"密码框\",\"fieldId\":\"APPLY_USER_IDS\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"password\",\"key\":\"a123996b4fda48f3ba31b292d8ab7abc\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"id\",\"fieldId\":\"APPLY_DEP_NAME\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"textarea\",\"key\":\"cc2d550b81b84cefb5d762af23be9abd\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"姓名\",\"fieldId\":\"APPLY_NUMBER\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"input\",\"key\":\"1bd03aaf9afe42a29b374216ce0a87db\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"密码\",\"fieldId\":\"THEME\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"password\",\"key\":\"40602abf92f24613b91c60e3d9418c12\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"单行文本1\",\"fieldId\":\"REMARK\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"input-cs\",\"key\":\"446fe70b255a43c492757b761fe2c59a\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"多行文本\",\"fieldId\":\"FILE_PATH\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"textarea\",\"key\":\"0f4ec3872af54bebbebbe64954131428\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"计数组件\",\"fieldId\":\"APPLY_DEP_ID\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"number\",\"key\":\"fe86f655d7fa498487d80ed66e385cb2\",\"children\":[]}]}],\"noticePolicyConfigs\":[],\"approverConfigs\":[],\"circulateConfigs\":[],\"autoAgreeRule\":[],\"isPrevChooseNext\":0,\"provisionalApprover\":false,\"noHandler\":0,\"countersignConfig\":{\"multipleInstancesType\":0,\"addOrRemove\":1,\"finishType\":0,\"percentage\":0,\"countersignList\":[]},\"buttonConfigs\":[{\"buttonType\":0,\"buttonName\":\"同意\",\"buttonCode\":\"agree\",\"approveType\":0,\"checked\":true},{\"buttonType\":0,\"buttonName\":\"拒绝\",\"buttonCode\":\"disagree\",\"approveType\":1,\"checked\":true},{\"buttonType\":0,\"buttonName\":\"驳回\",\"buttonCode\":\"reject\",\"approveType\":2,\"checked\":true,\"buttonOpera\":0},{\"buttonType\":0,\"buttonName\":\"结束\",\"buttonCode\":\"finish\",\"approveType\":3,\"checked\":true}],\"opinionConfig\":{\"enabled\":false,\"showType\":0,\"signature\":0,\"component\":[]},\"assignmentConfig\":{\"formAssignmentConfigs\":[],\"paramAssignmentConfigs\":[]},\"timeOutHandle\":{\"isHandle\":1,\"rule\":1,\"type\":2,\"user\":1,\"auto\":1},\"startEventConfigs\":[],\"endEventConfigs\":[],\"status\":\"1\"},{\"id\":\"Flow_0q8p2ki\",\"type\":\"bpmn:SequenceFlow\",\"name\":\"流程线\",\"parentId\":\"process_jiun5e7f\",\"remark\":\"\",\"conditionConfigs\":[],\"startEventConfigs\":[],\"endEventConfigs\":[]},{\"id\":\"Flow_17atnar\",\"type\":\"bpmn:SequenceFlow\",\"name\":\"流程线\",\"parentId\":\"process_jiun5e7f\",\"remark\":\"\",\"conditionConfigs\":[],\"startEventConfigs\":[],\"endEventConfigs\":[]},{\"id\":\"Activity_0qnb6yk\",\"type\":\"bpmn:UserTask\",\"name\":\"领导审批\",\"parentId\":\"process_jiun5e7f\",\"remark\":\"\",\"formConfigs\":[{\"key\":\"form_1788462521134452737_1716025688806\",\"formType\":1,\"formId\":\"1788462521134452737\",\"formName\":\"编辑视图参数测速\",\"name\":\"编辑视图参数测速\",\"showChildren\":true,\"requiredAll\":true,\"viewAll\":true,\"editAll\":true,\"children\":[{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"密码框\",\"fieldId\":\"APPLY_USER_IDS\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"password\",\"key\":\"a123996b4fda48f3ba31b292d8ab7abc\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"id\",\"fieldId\":\"APPLY_DEP_NAME\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"textarea\",\"key\":\"cc2d550b81b84cefb5d762af23be9abd\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"姓名\",\"fieldId\":\"APPLY_NUMBER\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"input\",\"key\":\"1bd03aaf9afe42a29b374216ce0a87db\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"密码\",\"fieldId\":\"THEME\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"password\",\"key\":\"40602abf92f24613b91c60e3d9418c12\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"单行文本1\",\"fieldId\":\"REMARK\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"input-cs\",\"key\":\"446fe70b255a43c492757b761fe2c59a\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"多行文本\",\"fieldId\":\"FILE_PATH\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"textarea\",\"key\":\"0f4ec3872af54bebbebbe64954131428\",\"children\":[]},{\"required\":true,\"view\":true,\"edit\":true,\"disabled\":false,\"isSaveTable\":false,\"tableName\":\"\",\"fieldName\":\"计数组件\",\"fieldId\":\"APPLY_DEP_ID\",\"isSubTable\":false,\"showChildren\":true,\"type\":\"number\",\"key\":\"fe86f655d7fa498487d80ed66e385cb2\",\"children\":[]}]}],\"noticePolicyConfigs\":[],\"approverConfigs\":[],\"circulateConfigs\":[],\"autoAgreeRule\":[],\"isPrevChooseNext\":0,\"provisionalApprover\":false,\"noHandler\":0,\"countersignConfig\":{\"multipleInstancesType\":0,\"addOrRemove\":1,\"finishType\":0,\"percentage\":0,\"countersignList\":[]},\"buttonConfigs\":[{\"buttonType\":0,\"buttonName\":\"同意\",\"buttonCode\":\"agree\",\"approveType\":0,\"checked\":true},{\"buttonType\":0,\"buttonName\":\"拒绝\",\"buttonCode\":\"disagree\",\"approveType\":1,\"checked\":true},{\"buttonType\":0,\"buttonName\":\"驳回\",\"buttonCode\":\"reject\",\"approveType\":2,\"checked\":true,\"buttonOpera\":0},{\"buttonType\":0,\"buttonName\":\"结束\",\"buttonCode\":\"finish\",\"approveType\":3,\"checked\":true}],\"opinionConfig\":{\"enabled\":false,\"showType\":0,\"signature\":0,\"component\":[]},\"assignmentConfig\":{\"formAssignmentConfigs\":[],\"paramAssignmentConfigs\":[]},\"timeOutHandle\":{\"isHandle\":1,\"rule\":1,\"type\":2,\"user\":1,\"auto\":1},\"startEventConfigs\":[],\"endEventConfigs\":[],\"status\":\"2\"},{\"id\":\"Flow_0sjhs4f\",\"type\":\"bpmn:SequenceFlow\",\"name\":\"流程线\",\"parentId\":\"process_jiun5e7f\",\"remark\":\"\",\"conditionConfigs\":[],\"startEventConfigs\":[],\"endEventConfigs\":[]},{\"id\":\"Event_07q1c4l\",\"type\":\"bpmn:EndEvent\",\"name\":\"结束节点\",\"parentId\":\"process_jiun5e7f\",\"remark\":\"\",\"noticePolicyConfigs\":[],\"startEventConfigs\":[],\"endEventConfigs\":[],\"status\":\"999\"}]}", "jsonContentObj": {"childNodeConfig": [{"startEventConfigs": [], "formConfigs": [{"formId": "1788462521134452737", "editAll": true, "formType": 1, "children": [{"fieldName": "密码框", "edit": true, "type": "password", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "a123996b4fda48f3ba31b292d8ab7abc", "fieldId": "APPLY_USER_IDS"}, {"fieldName": "id", "edit": true, "type": "textarea", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "cc2d550b81b84cefb5d762af23be9abd", "fieldId": "APPLY_DEP_NAME"}, {"fieldName": "姓名", "edit": true, "type": "input", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "1bd03aaf9afe42a29b374216ce0a87db", "fieldId": "APPLY_NUMBER"}, {"fieldName": "密码", "edit": true, "type": "password", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "40602abf92f24613b91c60e3d9418c12", "fieldId": "THEME"}, {"fieldName": "单行文本1", "edit": true, "type": "input-cs", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "446fe70b255a43c492757b761fe2c59a", "fieldId": "REMARK"}, {"fieldName": "多行文本", "edit": true, "type": "textarea", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "0f4ec3872af54bebbebbe64954131428", "fieldId": "FILE_PATH"}, {"fieldName": "计数组件", "edit": true, "type": "number", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "fe86f655d7fa498487d80ed66e385cb2", "fieldId": "APPLY_DEP_ID"}], "requiredAll": true, "formName": "编辑视图参数测速", "name": "编辑视图参数测速", "key": "form_1788462521134452737_1716025688806", "showChildren": true, "viewAll": true}], "name": "开始节点", "subProcessInitiator": "", "endEventConfigs": [], "remark": "", "id": "Event_start_node", "type": "bpmn:StartEvent", "parentId": "process_jiun5e7f", "assignmentConfig": {"paramAssignmentConfigs": [], "formAssignmentConfigs": []}, "status": "0"}, {"approverConfigs": [], "provisionalApprover": false, "noticePolicyConfigs": [], "autoAgreeRule": [], "noHandler": 0, "buttonConfigs": [{"buttonName": "同意", "approveType": 0, "buttonType": 0, "buttonCode": "agree", "checked": true}, {"buttonName": "拒绝", "approveType": 1, "buttonType": 0, "buttonCode": "disagree", "checked": true}, {"buttonName": "驳回", "approveType": 2, "buttonType": 0, "buttonCode": "reject", "checked": true, "buttonOpera": 0}, {"buttonName": "结束", "approveType": 3, "buttonType": 0, "buttonCode": "finish", "checked": true}], "remark": "", "type": "bpmn:UserTask", "opinionConfig": {"component": [], "signature": 0, "showType": 0, "enabled": false}, "parentId": "process_jiun5e7f", "countersignConfig": {"percentage": 0, "multipleInstancesType": 0, "countersignList": [], "addOrRemove": 1, "finishType": 0}, "circulateConfigs": [], "startEventConfigs": [], "formConfigs": [{"formId": "1788462521134452737", "editAll": true, "formType": 1, "children": [{"fieldName": "密码框", "edit": true, "type": "password", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "a123996b4fda48f3ba31b292d8ab7abc", "fieldId": "APPLY_USER_IDS"}, {"fieldName": "id", "edit": true, "type": "textarea", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "cc2d550b81b84cefb5d762af23be9abd", "fieldId": "APPLY_DEP_NAME"}, {"fieldName": "姓名", "edit": true, "type": "input", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "1bd03aaf9afe42a29b374216ce0a87db", "fieldId": "APPLY_NUMBER"}, {"fieldName": "密码", "edit": true, "type": "password", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "40602abf92f24613b91c60e3d9418c12", "fieldId": "THEME"}, {"fieldName": "单行文本1", "edit": true, "type": "input-cs", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "446fe70b255a43c492757b761fe2c59a", "fieldId": "REMARK"}, {"fieldName": "多行文本", "edit": true, "type": "textarea", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "0f4ec3872af54bebbebbe64954131428", "fieldId": "FILE_PATH"}, {"fieldName": "计数组件", "edit": true, "type": "number", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "fe86f655d7fa498487d80ed66e385cb2", "fieldId": "APPLY_DEP_ID"}], "requiredAll": true, "formName": "编辑视图参数测速", "name": "编辑视图参数测速", "key": "form_1788462521134452737_1716025688806", "showChildren": true, "viewAll": true}], "name": "发起审批", "endEventConfigs": [], "id": "Activity_0eu2df5", "isPrevChooseNext": 0, "timeOutHandle": {"auto": 1, "rule": 1, "type": 2, "isHandle": 1, "user": 1}, "assignmentConfig": {"paramAssignmentConfigs": [], "formAssignmentConfigs": []}, "status": "1"}, {"startEventConfigs": [], "name": "流程线", "endEventConfigs": [], "remark": "", "id": "Flow_0q8p2ki", "type": "bpmn:SequenceFlow", "conditionConfigs": [], "parentId": "process_jiun5e7f"}, {"startEventConfigs": [], "name": "流程线", "endEventConfigs": [], "remark": "", "id": "Flow_17atnar", "type": "bpmn:SequenceFlow", "conditionConfigs": [], "parentId": "process_jiun5e7f"}, {"approverConfigs": [], "provisionalApprover": false, "noticePolicyConfigs": [], "autoAgreeRule": [], "noHandler": 0, "buttonConfigs": [{"buttonName": "同意", "approveType": 0, "buttonType": 0, "buttonCode": "agree", "checked": true}, {"buttonName": "拒绝", "approveType": 1, "buttonType": 0, "buttonCode": "disagree", "checked": true}, {"buttonName": "驳回", "approveType": 2, "buttonType": 0, "buttonCode": "reject", "checked": true, "buttonOpera": 0}, {"buttonName": "结束", "approveType": 3, "buttonType": 0, "buttonCode": "finish", "checked": true}], "remark": "", "type": "bpmn:UserTask", "opinionConfig": {"component": [], "signature": 0, "showType": 0, "enabled": false}, "parentId": "process_jiun5e7f", "countersignConfig": {"percentage": 0, "multipleInstancesType": 0, "countersignList": [], "addOrRemove": 1, "finishType": 0}, "circulateConfigs": [], "startEventConfigs": [], "formConfigs": [{"formId": "1788462521134452737", "editAll": true, "formType": 1, "children": [{"fieldName": "密码框", "edit": true, "type": "password", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "a123996b4fda48f3ba31b292d8ab7abc", "fieldId": "APPLY_USER_IDS"}, {"fieldName": "id", "edit": true, "type": "textarea", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "cc2d550b81b84cefb5d762af23be9abd", "fieldId": "APPLY_DEP_NAME"}, {"fieldName": "姓名", "edit": true, "type": "input", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "1bd03aaf9afe42a29b374216ce0a87db", "fieldId": "APPLY_NUMBER"}, {"fieldName": "密码", "edit": true, "type": "password", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "40602abf92f24613b91c60e3d9418c12", "fieldId": "THEME"}, {"fieldName": "单行文本1", "edit": true, "type": "input-cs", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "446fe70b255a43c492757b761fe2c59a", "fieldId": "REMARK"}, {"fieldName": "多行文本", "edit": true, "type": "textarea", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "0f4ec3872af54bebbebbe64954131428", "fieldId": "FILE_PATH"}, {"fieldName": "计数组件", "edit": true, "type": "number", "required": true, "tableName": "", "view": true, "isSaveTable": false, "children": [], "isSubTable": false, "disabled": false, "showChildren": true, "key": "fe86f655d7fa498487d80ed66e385cb2", "fieldId": "APPLY_DEP_ID"}], "requiredAll": true, "formName": "编辑视图参数测速", "name": "编辑视图参数测速", "key": "form_1788462521134452737_1716025688806", "showChildren": true, "viewAll": true}], "name": "领导审批", "endEventConfigs": [], "id": "Activity_0qnb6yk", "isPrevChooseNext": 0, "timeOutHandle": {"auto": 1, "rule": 1, "type": 2, "isHandle": 1, "user": 1}, "assignmentConfig": {"paramAssignmentConfigs": [], "formAssignmentConfigs": []}, "status": "2"}, {"startEventConfigs": [], "name": "流程线", "endEventConfigs": [], "remark": "", "id": "Flow_0sjhs4f", "type": "bpmn:SequenceFlow", "conditionConfigs": [], "parentId": "process_jiun5e7f"}, {"startEventConfigs": [], "noticePolicyConfigs": [], "name": "结束节点", "endEventConfigs": [], "remark": "", "id": "Event_07q1c4l", "type": "bpmn:EndEvent", "parentId": "process_jiun5e7f", "status": "999"}], "processConfig": {"menuConfig": {"code": "", "name": "", "icon": "", "remark": "", "sortCode": null, "parentId": null}, "code": "2024", "formInitConfig": {"formId": null, "formType": 1, "formName": "", "enabled": false}, "processParamConfigs": [], "authConfig": {"authMemberConfigs": [], "authType": 0}, "autoAgreeRule": [], "noHandler": 0, "nameRuleConfigs": [], "remark": "", "workflowChat": "http://202.168.189.182:19000/xjrsoft/20240518/7fcffe3490ec4ac89f70c3cf5a6ea57f.svg", "relationProcessConfigs": [], "appShow": false, "nameRule": "", "defaultFormList": [{"formId": "1788462521134452737", "editAll": null, "formType": 1, "children": null, "requiredAll": null, "formName": "编辑视图参数测速", "key": "form_1788462521134452737_1716025688806", "showChildren": null, "viewAll": null}], "processId": "process_jiun5e7f", "name": "新增测试模板", "category": "1630814561619472386", "implStartNodeForm": null, "isPrevChooseNext": 0, "timeoutRemidConfig": {"hour": null, "interval": null, "enabled": false, "pushHits": 1, "pushMemberConfigs": []}, "xmlContent": ""}}}, "relationTasks": null, "formAssignmentData": null, "workflowChat": "http://202.168.189.182:19000/xjrsoft/20240518/7fcffe3490ec4ac89f70c3cf5a6ea57f.svg"}, "success": true}